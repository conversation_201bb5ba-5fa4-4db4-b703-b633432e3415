{% extends "base.html" %}

{% block title %}
    {% if current_language == 'ar' %}تسجيل الدخول{% else %}Login{% endif %} - Billing System
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header text-center bg-primary text-white">
                <h4>
                    {% if current_language == 'ar' %}
                        تسجيل الدخول
                    {% else %}
                        Login
                    {% endif %}
                </h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="username" class="form-label">
                            {% if current_language == 'ar' %}اسم المستخدم{% else %}Username{% endif %}
                        </label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">
                            {% if current_language == 'ar' %}كلمة المرور{% else %}Password{% endif %}
                        </label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            {% if current_language == 'ar' %}دخول{% else %}Login{% endif %}
                        </button>
                    </div>
                </form>
                
                <div class="mt-3 text-center">
                    <small class="text-muted">
                        {% if current_language == 'ar' %}
                            المستخدم الافتراضي: admin | كلمة المرور: admin123
                        {% else %}
                            Default user: admin | Password: admin123
                        {% endif %}
                    </small>
                </div>
            </div>
        </div>
        
        <!-- Language selector -->
        <div class="text-center mt-3">
            <div class="btn-group" role="group">
                <a href="{{ url_for('set_language', language='en') }}" 
                   class="btn btn-outline-secondary {{ 'active' if current_language == 'en' else '' }}">
                    English
                </a>
                <a href="{{ url_for('set_language', language='ar') }}" 
                   class="btn btn-outline-secondary {{ 'active' if current_language == 'ar' else '' }}">
                    العربية
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
