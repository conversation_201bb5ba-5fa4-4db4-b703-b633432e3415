from flask import Flask, render_template, request, redirect, url_for, flash, session, send_file, jsonify
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from werkzeug.utils import secure_filename
import os
from datetime import datetime, date
from config import Config
from models import db, User, Customer, Product, Invoice, InvoiceItem, CompanySettings
from utils import generate_invoice_number, create_pdf_invoice, ensure_upload_folders

app = Flask(__name__)
app.config.from_object(Config)

# Initialize extensions
db.init_app(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Language helper
def get_language():
    return session.get('language', 'en')

@app.context_processor
def inject_language():
    return dict(current_language=get_language())

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/set_language/<language>')
def set_language(language):
    if language in app.config['LANGUAGES']:
        session['language'] = language
    return redirect(request.referrer or url_for('index'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            login_user(user)
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid username or password', 'error')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Get statistics
    total_customers = Customer.query.count()
    total_products = Product.query.count()
    total_invoices = Invoice.query.count()
    
    # Recent invoices
    recent_invoices = Invoice.query.order_by(Invoice.created_at.desc()).limit(5).all()
    
    # Calculate total revenue
    total_revenue = db.session.query(db.func.sum(Invoice.total_amount)).scalar() or 0
    
    stats = {
        'total_customers': total_customers,
        'total_products': total_products,
        'total_invoices': total_invoices,
        'total_revenue': total_revenue,
        'recent_invoices': recent_invoices
    }
    
    return render_template('dashboard.html', stats=stats)

@app.route('/customers')
@login_required
def customers():
    page = request.args.get('page', 1, type=int)
    customers = Customer.query.paginate(
        page=page, per_page=app.config['ITEMS_PER_PAGE'], error_out=False
    )
    return render_template('customers.html', customers=customers)

@app.route('/customers/add', methods=['GET', 'POST'])
@login_required
def add_customer():
    if request.method == 'POST':
        customer = Customer(
            name_en=request.form['name_en'],
            name_ar=request.form.get('name_ar', ''),
            email=request.form.get('email', ''),
            phone=request.form.get('phone', ''),
            address_en=request.form.get('address_en', ''),
            address_ar=request.form.get('address_ar', ''),
            tax_number=request.form.get('tax_number', '')
        )
        db.session.add(customer)
        db.session.commit()
        flash('Customer added successfully!', 'success')
        return redirect(url_for('customers'))
    
    return render_template('add_customer.html')

@app.route('/customers/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_customer(id):
    customer = Customer.query.get_or_404(id)
    
    if request.method == 'POST':
        customer.name_en = request.form['name_en']
        customer.name_ar = request.form.get('name_ar', '')
        customer.email = request.form.get('email', '')
        customer.phone = request.form.get('phone', '')
        customer.address_en = request.form.get('address_en', '')
        customer.address_ar = request.form.get('address_ar', '')
        customer.tax_number = request.form.get('tax_number', '')
        
        db.session.commit()
        flash('Customer updated successfully!', 'success')
        return redirect(url_for('customers'))
    
    return render_template('edit_customer.html', customer=customer)

@app.route('/products')
@login_required
def products():
    page = request.args.get('page', 1, type=int)
    products = Product.query.paginate(
        page=page, per_page=app.config['ITEMS_PER_PAGE'], error_out=False
    )
    return render_template('products.html', products=products)

@app.route('/products/add', methods=['GET', 'POST'])
@login_required
def add_product():
    if request.method == 'POST':
        product = Product(
            name_en=request.form['name_en'],
            name_ar=request.form.get('name_ar', ''),
            description_en=request.form.get('description_en', ''),
            description_ar=request.form.get('description_ar', ''),
            price=float(request.form['price']),
            tax_rate=float(request.form.get('tax_rate', 0))
        )
        db.session.add(product)
        db.session.commit()
        flash('Product added successfully!', 'success')
        return redirect(url_for('products'))
    
    return render_template('add_product.html')

@app.route('/invoices')
@login_required
def invoices():
    page = request.args.get('page', 1, type=int)
    invoices = Invoice.query.order_by(Invoice.created_at.desc()).paginate(
        page=page, per_page=app.config['ITEMS_PER_PAGE'], error_out=False
    )
    return render_template('invoices.html', invoices=invoices)

@app.route('/invoices/create', methods=['GET', 'POST'])
@login_required
def create_invoice():
    if request.method == 'POST':
        # Create new invoice
        invoice = Invoice(
            invoice_number=generate_invoice_number(),
            customer_id=int(request.form['customer_id']),
            issue_date=datetime.strptime(request.form['issue_date'], '%Y-%m-%d').date(),
            due_date=datetime.strptime(request.form['due_date'], '%Y-%m-%d').date() if request.form.get('due_date') else None,
            notes_en=request.form.get('notes_en', ''),
            notes_ar=request.form.get('notes_ar', '')
        )
        
        db.session.add(invoice)
        db.session.flush()  # Get the invoice ID
        
        # Add invoice items
        items_data = request.form.getlist('items')
        subtotal = 0
        tax_amount = 0
        
        for item_json in items_data:
            import json
            item = json.loads(item_json)
            
            line_total = float(item['quantity']) * float(item['unit_price'])
            line_tax = line_total * (float(item['tax_rate']) / 100)
            
            invoice_item = InvoiceItem(
                invoice_id=invoice.id,
                product_id=item.get('product_id'),
                description_en=item['description_en'],
                description_ar=item.get('description_ar', ''),
                quantity=float(item['quantity']),
                unit_price=float(item['unit_price']),
                tax_rate=float(item['tax_rate']),
                line_total=line_total
            )
            
            db.session.add(invoice_item)
            subtotal += line_total
            tax_amount += line_tax
        
        # Update invoice totals
        invoice.subtotal = subtotal
        invoice.tax_amount = tax_amount
        invoice.total_amount = subtotal + tax_amount
        
        db.session.commit()
        flash('Invoice created successfully!', 'success')
        return redirect(url_for('view_invoice', id=invoice.id))
    
    customers = Customer.query.all()
    products = Product.query.all()
    today = date.today()
    return render_template('create_invoice.html', customers=customers, products=products, today=today)

@app.route('/invoices/<int:id>')
@login_required
def view_invoice(id):
    invoice = Invoice.query.get_or_404(id)
    return render_template('view_invoice.html', invoice=invoice)

@app.route('/invoices/<int:id>/pdf/<language>')
@login_required
def download_invoice_pdf(id, language):
    invoice = Invoice.query.get_or_404(id)
    
    try:
        pdf_path = create_pdf_invoice(invoice, language)
        return send_file(pdf_path, as_attachment=True, 
                        download_name=f"invoice_{invoice.invoice_number}_{language}.pdf")
    except Exception as e:
        flash(f'Error generating PDF: {str(e)}', 'error')
        return redirect(url_for('view_invoice', id=id))

# Initialize database
def create_tables():
    db.create_all()
    
    # Create default admin user if not exists
    if not User.query.filter_by(username='admin').first():
        admin = User(username='admin', email='<EMAIL>', is_admin=True)
        admin.set_password('admin123')
        db.session.add(admin)
        db.session.commit()
    
    # Ensure upload folders exist
    ensure_upload_folders()

if __name__ == '__main__':
    with app.app_context():
        create_tables()
    app.run(host='0.0.0.0', port=2121, debug=True)
