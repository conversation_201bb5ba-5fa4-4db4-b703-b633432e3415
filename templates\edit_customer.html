{% extends "base.html" %}

{% block title %}
    {% if current_language == 'ar' %}تعديل عميل{% else %}Edit Customer{% endif %} - Billing System
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                {% if current_language == 'ar' %}تعديل العميل{% else %}Edit Customer{% endif %}
            </h1>
            <a href="{{ url_for('customers') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                {% if current_language == 'ar' %}العودة{% else %}Back{% endif %}
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    {% if current_language == 'ar' %}معلومات العميل{% else %}Customer Information{% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name_en" class="form-label">
                                {% if current_language == 'ar' %}الاسم (إنجليزي) *{% else %}Name (English) *{% endif %}
                            </label>
                            <input type="text" class="form-control" id="name_en" name="name_en" 
                                   value="{{ customer.name_en }}" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="name_ar" class="form-label">
                                {% if current_language == 'ar' %}الاسم (عربي){% else %}Name (Arabic){% endif %}
                            </label>
                            <input type="text" class="form-control" id="name_ar" name="name_ar" 
                                   value="{{ customer.name_ar or '' }}"
                                   {% if current_language == 'ar' %}dir="rtl"{% endif %}>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                {% if current_language == 'ar' %}البريد الإلكتروني{% else %}Email{% endif %}
                            </label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ customer.email or '' }}">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">
                                {% if current_language == 'ar' %}رقم الهاتف{% else %}Phone Number{% endif %}
                            </label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="{{ customer.phone or '' }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="address_en" class="form-label">
                                {% if current_language == 'ar' %}العنوان (إنجليزي){% else %}Address (English){% endif %}
                            </label>
                            <textarea class="form-control" id="address_en" name="address_en" rows="3">{{ customer.address_en or '' }}</textarea>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="address_ar" class="form-label">
                                {% if current_language == 'ar' %}العنوان (عربي){% else %}Address (Arabic){% endif %}
                            </label>
                            <textarea class="form-control" id="address_ar" name="address_ar" rows="3"
                                      {% if current_language == 'ar' %}dir="rtl"{% endif %}>{{ customer.address_ar or '' }}</textarea>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="tax_number" class="form-label">
                                {% if current_language == 'ar' %}الرقم الضريبي{% else %}Tax Number{% endif %}
                            </label>
                            <input type="text" class="form-control" id="tax_number" name="tax_number" 
                                   value="{{ customer.tax_number or '' }}">
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('customers') }}" class="btn btn-secondary">
                            {% if current_language == 'ar' %}إلغاء{% else %}Cancel{% endif %}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            {% if current_language == 'ar' %}حفظ التغييرات{% else %}Save Changes{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    {% if current_language == 'ar' %}معلومات إضافية{% else %}Additional Info{% endif %}
                </h6>
            </div>
            <div class="card-body">
                <p><strong>
                    {% if current_language == 'ar' %}تاريخ الإنشاء:{% else %}Created:{% endif %}
                </strong> {{ customer.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                
                <p><strong>
                    {% if current_language == 'ar' %}عدد الفواتير:{% else %}Total Invoices:{% endif %}
                </strong> {{ customer.invoices|length }}</p>
                
                {% if customer.invoices %}
                <hr>
                <h6>
                    {% if current_language == 'ar' %}آخر الفواتير{% else %}Recent Invoices{% endif %}
                </h6>
                <ul class="list-unstyled">
                    {% for invoice in customer.invoices[:3] %}
                    <li class="mb-1">
                        <a href="{{ url_for('view_invoice', id=invoice.id) }}" class="text-decoration-none">
                            {{ invoice.invoice_number }}
                        </a>
                        <small class="text-muted d-block">${{ "%.2f"|format(invoice.total_amount) }}</small>
                    </li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
