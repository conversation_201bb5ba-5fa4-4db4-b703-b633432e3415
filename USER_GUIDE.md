# دليل المستخدم - نظام الفواتير الثنائي اللغة
# User Guide - Bilingual Billing System

## 🚀 البدء السريع / Quick Start

### التشغيل / Running the System
```bash
# تثبيت المتطلبات / Install requirements
pip install -r requirements.txt

# تشغيل النظام / Run the system
python run.py

# أو / Or
python app.py
```

### الوصول للنظام / Access the System
- **الرابط / URL**: http://localhost:2121
- **المستخدم / Username**: admin
- **كلمة المرور / Password**: admin123

---

## 📋 الميزات الرئيسية / Main Features

### 1. 🏠 لوحة التحكم / Dashboard
- **الإحصائيات / Statistics**: عرض إجمالي العملاء والمنتجات والفواتير والإيرادات
- **الفواتير الحديثة / Recent Invoices**: آخر 5 فواتير تم إنشاؤها
- **الإجراءات السريعة / Quick Actions**: أزرار سريعة للمهام الشائعة

### 2. 👥 إدارة العملاء / Customer Management

#### إضافة عميل جديد / Add New Customer
1. انقر على "العملاء" → "إضافة عميل" / Click "Customers" → "Add Customer"
2. املأ البيانات المطلوبة / Fill required information:
   - **الاسم (إنجليزي) / Name (English)**: مطلوب / Required
   - **الاسم (عربي) / Name (Arabic)**: اختياري / Optional
   - **البريد الإلكتروني / Email**: اختياري / Optional
   - **الهاتف / Phone**: اختياري / Optional
   - **العنوان / Address**: بالعربية والإنجليزية / Arabic & English
   - **الرقم الضريبي / Tax Number**: اختياري / Optional

#### البحث في العملاء / Search Customers
- استخدم شريط البحث للبحث بالاسم أو البريد أو الهاتف
- Use search bar to find by name, email, or phone

#### تعديل/حذف العملاء / Edit/Delete Customers
- **تعديل / Edit**: انقر على زر "تعديل" / Click "Edit" button
- **حذف / Delete**: انقر على زر "حذف" (فقط إذا لم توجد فواتير) / Click "Delete" (only if no invoices exist)

### 3. 📦 إدارة المنتجات / Product Management

#### إضافة منتج جديد / Add New Product
1. انقر على "المنتجات" → "إضافة منتج" / Click "Products" → "Add Product"
2. املأ البيانات / Fill information:
   - **اسم المنتج / Product Name**: بالعربية والإنجليزية / Arabic & English
   - **السعر / Price**: مطلوب / Required
   - **معدل الضريبة / Tax Rate**: نسبة مئوية / Percentage
   - **الوصف / Description**: تفصيلي / Detailed description

#### معاينة السعر / Price Preview
- يتم حساب السعر الإجمالي تلقائياً مع الضريبة
- Total price calculated automatically with tax

### 4. 🧾 إدارة الفواتير / Invoice Management

#### إنشاء فاتورة جديدة / Create New Invoice
1. انقر على "الفواتير" → "إنشاء فاتورة" / Click "Invoices" → "Create Invoice"
2. اختر العميل / Select customer
3. حدد تاريخ الإصدار والاستحقاق / Set issue and due dates
4. أضف العناصر / Add items:
   - **من الكتالوج / From Catalog**: اختر منتج موجود / Select existing product
   - **مخصص / Custom**: أدخل تفاصيل مخصصة / Enter custom details
5. أضف ملاحظات (اختياري) / Add notes (optional)
6. احفظ الفاتورة / Save invoice

#### البحث والتصفية / Search & Filter
- **البحث / Search**: برقم الفاتورة أو اسم العميل / By invoice number or customer name
- **التصفية / Filter**: حسب الحالة (مسودة، مرسلة، مدفوعة، ملغية) / By status

#### تحديث حالة الفاتورة / Update Invoice Status
1. افتح الفاتورة / Open invoice
2. انقر على "تغيير الحالة" / Click "Change Status"
3. اختر الحالة الجديدة / Select new status:
   - **مسودة / Draft**: قيد التحضير / In preparation
   - **مرسلة / Sent**: تم إرسالها للعميل / Sent to customer
   - **مدفوعة / Paid**: تم الدفع / Payment received
   - **ملغية / Cancelled**: ملغية / Cancelled

#### تحميل PDF / Download PDF
- **إنجليزي / English**: فاتورة بالإنجليزية / English invoice
- **عربي / Arabic**: فاتورة بالعربية مع تنسيق RTL / Arabic invoice with RTL formatting

### 5. 📊 التقارير / Reports

#### تقرير مالي / Financial Report
1. انقر على "التقارير" / Click "Reports"
2. اختر الفترة الزمنية (اختياري) / Select date range (optional)
3. انقر على "تطبيق" / Click "Apply"

#### محتويات التقرير / Report Contents
- **إجمالي الإيرادات / Total Revenue**: مجموع كل الفواتير / Sum of all invoices
- **إجمالي الضرائب / Total Tax**: مجموع الضرائب / Sum of taxes
- **عدد الفواتير / Invoice Count**: العدد الكلي / Total count
- **متوسط الفاتورة / Average Invoice**: المتوسط / Average amount
- **الفواتير حسب الحالة / Invoices by Status**: توزيع الحالات / Status distribution
- **أفضل العملاء / Top Customers**: أكثر العملاء إنفاقاً / Highest spending customers

#### تصدير البيانات / Export Data
- انقر على "تصدير CSV" لحفظ البيانات في ملف Excel
- Click "Export CSV" to save data to Excel file

### 6. ⚙️ إعدادات الشركة / Company Settings

#### تحديث معلومات الشركة / Update Company Information
1. انقر على "الإعدادات" / Click "Settings"
2. املأ البيانات / Fill information:
   - **اسم الشركة / Company Name**: بالعربية والإنجليزية / Arabic & English
   - **العنوان / Address**: عنوان مفصل / Detailed address
   - **الهاتف / Phone**: رقم الاتصال / Contact number
   - **البريد الإلكتروني / Email**: البريد الرسمي / Official email
   - **الموقع الإلكتروني / Website**: رابط الموقع / Website URL
   - **الرقم الضريبي / Tax Number**: الرقم الضريبي الرسمي / Official tax number

#### رفع شعار الشركة / Upload Company Logo
- الحجم المثالي: 300x150 بكسل / Ideal size: 300x150 pixels
- الصيغ المدعومة: PNG, JPG / Supported formats: PNG, JPG

---

## 🌐 الدعم الثنائي اللغة / Bilingual Support

### تبديل اللغة / Language Switching
- انقر على زر اللغة في الشريط العلوي / Click language button in top bar
- **English**: واجهة إنجليزية / English interface
- **العربية**: واجهة عربية مع دعم RTL / Arabic interface with RTL support

### محتوى ثنائي اللغة / Bilingual Content
- **العملاء / Customers**: أسماء وعناوين بالعربية والإنجليزية / Names and addresses in both languages
- **المنتجات / Products**: أسماء وأوصاف ثنائية اللغة / Bilingual names and descriptions
- **الفواتير / Invoices**: ملاحظات بكلا اللغتين / Notes in both languages
- **PDF**: إنشاء فواتير منفصلة لكل لغة / Separate PDFs for each language

---

## 🔧 استكشاف الأخطاء / Troubleshooting

### مشاكل شائعة / Common Issues

#### لا يمكن الوصول للنظام / Cannot Access System
```bash
# تحقق من تشغيل الخادم / Check if server is running
python test_system.py

# أعد تشغيل النظام / Restart system
python run.py
```

#### مشاكل قاعدة البيانات / Database Issues
```bash
# إعادة تهيئة قاعدة البيانات / Reset database
python init_db.py --reset
```

#### مشاكل PDF / PDF Issues
- تأكد من تثبيت جميع المكتبات / Ensure all libraries are installed
- تحقق من وجود مجلد static/invoices / Check static/invoices folder exists

### الحصول على المساعدة / Getting Help
- تحقق من ملف README.md للتفاصيل التقنية / Check README.md for technical details
- راجع ملفات السجل للأخطاء / Check log files for errors
- تأكد من تثبيت جميع المتطلبات / Ensure all requirements are installed

---

## 📈 نصائح للاستخدام الأمثل / Best Practices

### إدارة البيانات / Data Management
- **النسخ الاحتياطي / Backup**: انسخ ملف billing_system.db بانتظام / Regularly backup billing_system.db
- **التنظيم / Organization**: استخدم أسماء واضحة للعملاء والمنتجات / Use clear names for customers and products
- **التحديث / Updates**: حدث معلومات الشركة بانتظام / Keep company information updated

### الأمان / Security
- **كلمة المرور / Password**: غير كلمة مرور المدير الافتراضية / Change default admin password
- **النسخ الاحتياطي / Backup**: احتفظ بنسخ احتياطية آمنة / Keep secure backups
- **الوصول / Access**: قيد الوصول للمستخدمين المخولين فقط / Limit access to authorized users only

---

## 🎯 الخلاصة / Summary

نظام الفواتير الثنائي اللغة يوفر حلاً شاملاً لإدارة الفواتير للشركات التي تتعامل بالعربية والإنجليزية. النظام سهل الاستخدام ويدعم جميع العمليات الأساسية من إدارة العملاء والمنتجات إلى إنشاء فواتير احترافية وتقارير مالية مفصلة.

The Bilingual Billing System provides a comprehensive solution for invoice management for companies dealing in both Arabic and English. The system is user-friendly and supports all basic operations from customer and product management to creating professional invoices and detailed financial reports.
