import os

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///billing_system.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Language settings
    LANGUAGES = {
        'en': 'English',
        'ar': 'العربية'
    }
    
    # PDF settings
    PDF_UPLOAD_FOLDER = 'static/invoices'
    LOGO_UPLOAD_FOLDER = 'static/logos'
    
    # Application settings
    ITEMS_PER_PAGE = 10
    
    # Company default information
    COMPANY_NAME_EN = "Your Company Name"
    COMPANY_NAME_AR = "اسم شركتك"
    COMPANY_ADDRESS_EN = "Company Address"
    COMPANY_ADDRESS_AR = "عنوان الشركة"
    COMPANY_PHONE = "+1234567890"
    COMPANY_EMAIL = "<EMAIL>"
