{% extends "base.html" %}

{% block title %}
    {% if current_language == 'ar' %}لوحة التحكم{% else %}Dashboard{% endif %} - Billing System
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            {% if current_language == 'ar' %}
                لوحة التحكم
            {% else %}
                Dashboard
            {% endif %}
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.total_customers }}</h4>
                        <p class="mb-0">
                            {% if current_language == 'ar' %}العملاء{% else %}Customers{% endif %}
                        </p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.total_products }}</h4>
                        <p class="mb-0">
                            {% if current_language == 'ar' %}المنتجات{% else %}Products{% endif %}
                        </p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-box fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.total_invoices }}</h4>
                        <p class="mb-0">
                            {% if current_language == 'ar' %}الفواتير{% else %}Invoices{% endif %}
                        </p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-invoice fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>${{ "%.2f"|format(stats.total_revenue) }}</h4>
                        <p class="mb-0">
                            {% if current_language == 'ar' %}إجمالي الإيرادات{% else %}Total Revenue{% endif %}
                        </p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    {% if current_language == 'ar' %}إجراءات سريعة{% else %}Quick Actions{% endif %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('create_invoice') }}" class="btn btn-primary w-100">
                            <i class="fas fa-plus"></i>
                            {% if current_language == 'ar' %}إنشاء فاتورة{% else %}Create Invoice{% endif %}
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('add_customer') }}" class="btn btn-success w-100">
                            <i class="fas fa-user-plus"></i>
                            {% if current_language == 'ar' %}إضافة عميل{% else %}Add Customer{% endif %}
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('add_product') }}" class="btn btn-info w-100">
                            <i class="fas fa-box"></i>
                            {% if current_language == 'ar' %}إضافة منتج{% else %}Add Product{% endif %}
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('invoices') }}" class="btn btn-secondary w-100">
                            <i class="fas fa-list"></i>
                            {% if current_language == 'ar' %}عرض الفواتير{% else %}View Invoices{% endif %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Invoices -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    {% if current_language == 'ar' %}الفواتير الحديثة{% else %}Recent Invoices{% endif %}
                </h5>
            </div>
            <div class="card-body">
                {% if stats.recent_invoices %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>
                                    {% if current_language == 'ar' %}رقم الفاتورة{% else %}Invoice #{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}العميل{% else %}Customer{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}التاريخ{% else %}Date{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}المبلغ{% else %}Amount{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}الحالة{% else %}Status{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}إجراءات{% else %}Actions{% endif %}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in stats.recent_invoices %}
                            <tr>
                                <td>{{ invoice.invoice_number }}</td>
                                <td>
                                    {% if current_language == 'ar' and invoice.customer.name_ar %}
                                        {{ invoice.customer.name_ar }}
                                    {% else %}
                                        {{ invoice.customer.name_en }}
                                    {% endif %}
                                </td>
                                <td>{{ invoice.issue_date.strftime('%Y-%m-%d') }}</td>
                                <td>${{ "%.2f"|format(invoice.total_amount) }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if invoice.status == 'paid' else 'warning' }}">
                                        {{ invoice.status.title() }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ url_for('view_invoice', id=invoice.id) }}" class="btn btn-sm btn-outline-primary">
                                        {% if current_language == 'ar' %}عرض{% else %}View{% endif %}
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">
                    {% if current_language == 'ar' %}
                        لا توجد فواتير حتى الآن
                    {% else %}
                        No invoices yet
                    {% endif %}
                </p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
