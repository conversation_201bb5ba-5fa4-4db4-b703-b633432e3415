#!/usr/bin/env python3
"""
Database initialization script for the Billing System
This script creates the database tables and adds sample data
"""

from app import app
from models import db, User, Customer, Product, Invoice, InvoiceItem, CompanySettings
from datetime import datetime, date
import os

def init_database():
    """Initialize the database with tables and sample data"""
    
    with app.app_context():
        # Create all tables
        print("Creating database tables...")
        db.create_all()
        
        # Create default admin user if not exists
        if not User.query.filter_by(username='admin').first():
            print("Creating default admin user...")
            admin = User(username='admin', email='<EMAIL>', is_admin=True)
            admin.set_password('admin123')
            db.session.add(admin)
        
        # Create sample company settings
        if not CompanySettings.query.first():
            print("Creating default company settings...")
            company = CompanySettings(
                company_name_en="Your Company Name",
                company_name_ar="اسم شركتك",
                address_en="123 Business Street, City, Country",
                address_ar="123 شارع الأعمال، المدينة، البلد",
                phone="+1234567890",
                email="<EMAIL>",
                website="www.yourcompany.com",
                tax_number="TAX123456789"
            )
            db.session.add(company)
        
        # Create sample customers
        if Customer.query.count() == 0:
            print("Creating sample customers...")
            
            customers_data = [
                {
                    'name_en': 'Ahmed Al-Rashid',
                    'name_ar': 'أحمد الراشد',
                    'email': '<EMAIL>',
                    'phone': '+966501234567',
                    'address_en': '123 King Fahd Road, Riyadh, Saudi Arabia',
                    'address_ar': '123 طريق الملك فهد، الرياض، المملكة العربية السعودية',
                    'tax_number': 'VAT123456'
                },
                {
                    'name_en': 'Fatima Al-Zahra',
                    'name_ar': 'فاطمة الزهراء',
                    'email': '<EMAIL>',
                    'phone': '+966507654321',
                    'address_en': '456 Prince Sultan Street, Jeddah, Saudi Arabia',
                    'address_ar': '456 شارع الأمير سلطان، جدة، المملكة العربية السعودية',
                    'tax_number': 'VAT789012'
                },
                {
                    'name_en': 'Mohammed Al-Otaibi',
                    'name_ar': 'محمد العتيبي',
                    'email': '<EMAIL>',
                    'phone': '+966509876543',
                    'address_en': '789 Al-Khobar Corniche, Al-Khobar, Saudi Arabia',
                    'address_ar': '789 كورنيش الخبر، الخبر، المملكة العربية السعودية',
                    'tax_number': 'VAT345678'
                }
            ]
            
            for customer_data in customers_data:
                customer = Customer(**customer_data)
                db.session.add(customer)
        
        # Create sample products
        if Product.query.count() == 0:
            print("Creating sample products...")
            
            products_data = [
                {
                    'name_en': 'Web Development Service',
                    'name_ar': 'خدمة تطوير المواقع',
                    'description_en': 'Professional website development and design services',
                    'description_ar': 'خدمات تطوير وتصميم المواقع الإلكترونية المهنية',
                    'price': 2500.00,
                    'tax_rate': 15.0
                },
                {
                    'name_en': 'Mobile App Development',
                    'name_ar': 'تطوير تطبيقات الجوال',
                    'description_en': 'iOS and Android mobile application development',
                    'description_ar': 'تطوير تطبيقات الجوال لأنظمة iOS و Android',
                    'price': 5000.00,
                    'tax_rate': 15.0
                },
                {
                    'name_en': 'Digital Marketing Campaign',
                    'name_ar': 'حملة التسويق الرقمي',
                    'description_en': 'Comprehensive digital marketing and social media management',
                    'description_ar': 'التسويق الرقمي الشامل وإدارة وسائل التواصل الاجتماعي',
                    'price': 1500.00,
                    'tax_rate': 15.0
                },
                {
                    'name_en': 'SEO Optimization',
                    'name_ar': 'تحسين محركات البحث',
                    'description_en': 'Search engine optimization and website ranking improvement',
                    'description_ar': 'تحسين محركات البحث وتحسين ترتيب المواقع',
                    'price': 800.00,
                    'tax_rate': 15.0
                },
                {
                    'name_en': 'Graphic Design Package',
                    'name_ar': 'باقة التصميم الجرافيكي',
                    'description_en': 'Logo design, branding, and marketing materials',
                    'description_ar': 'تصميم الشعارات والهوية التجارية والمواد التسويقية',
                    'price': 1200.00,
                    'tax_rate': 15.0
                }
            ]
            
            for product_data in products_data:
                product = Product(**product_data)
                db.session.add(product)
        
        # Create sample invoices
        if Invoice.query.count() == 0:
            print("Creating sample invoices...")
            
            # Get customers and products
            customers = Customer.query.all()
            products = Product.query.all()
            
            if customers and products:
                # Invoice 1
                invoice1 = Invoice(
                    invoice_number="INV-2024-001",
                    customer_id=customers[0].id,
                    issue_date=date(2024, 1, 15),
                    due_date=date(2024, 2, 15),
                    notes_en="Thank you for choosing our services!",
                    notes_ar="شكراً لاختياركم خدماتنا!",
                    status="paid"
                )
                db.session.add(invoice1)
                db.session.flush()
                
                # Add items to invoice 1
                item1 = InvoiceItem(
                    invoice_id=invoice1.id,
                    product_id=products[0].id,
                    description_en=products[0].name_en,
                    description_ar=products[0].name_ar,
                    quantity=1,
                    unit_price=products[0].price,
                    tax_rate=products[0].tax_rate,
                    line_total=products[0].price
                )
                db.session.add(item1)
                
                # Calculate totals for invoice 1
                invoice1.subtotal = products[0].price
                invoice1.tax_amount = products[0].price * (products[0].tax_rate / 100)
                invoice1.total_amount = invoice1.subtotal + invoice1.tax_amount
                
                # Invoice 2
                invoice2 = Invoice(
                    invoice_number="INV-2024-002",
                    customer_id=customers[1].id,
                    issue_date=date(2024, 1, 20),
                    due_date=date(2024, 2, 20),
                    notes_en="Project includes 3 months of free support",
                    notes_ar="المشروع يتضمن 3 أشهر من الدعم المجاني",
                    status="sent"
                )
                db.session.add(invoice2)
                db.session.flush()
                
                # Add multiple items to invoice 2
                item2a = InvoiceItem(
                    invoice_id=invoice2.id,
                    product_id=products[1].id,
                    description_en=products[1].name_en,
                    description_ar=products[1].name_ar,
                    quantity=1,
                    unit_price=products[1].price,
                    tax_rate=products[1].tax_rate,
                    line_total=products[1].price
                )
                db.session.add(item2a)
                
                item2b = InvoiceItem(
                    invoice_id=invoice2.id,
                    product_id=products[2].id,
                    description_en=products[2].name_en,
                    description_ar=products[2].name_ar,
                    quantity=2,
                    unit_price=products[2].price,
                    tax_rate=products[2].tax_rate,
                    line_total=products[2].price * 2
                )
                db.session.add(item2b)
                
                # Calculate totals for invoice 2
                subtotal2 = products[1].price + (products[2].price * 2)
                tax2 = (products[1].price * products[1].tax_rate / 100) + (products[2].price * 2 * products[2].tax_rate / 100)
                invoice2.subtotal = subtotal2
                invoice2.tax_amount = tax2
                invoice2.total_amount = subtotal2 + tax2
        
        # Commit all changes
        db.session.commit()
        print("Database initialization completed successfully!")
        print("\nDefault login credentials:")
        print("Username: admin")
        print("Password: admin123")
        print("\nYou can now run the application with: python app.py")

def reset_database():
    """Reset the database by dropping and recreating all tables"""
    with app.app_context():
        print("Resetting database...")
        db.drop_all()
        init_database()

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == '--reset':
        reset_database()
    else:
        init_database()
