#!/usr/bin/env python3
"""
Test script to verify the billing system is working correctly
"""

import requests
import json

def test_system():
    """Test the billing system endpoints"""
    base_url = "http://localhost:2121"
    
    print("🧪 Testing Billing System...")
    
    # Test 1: Check if server is running
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print(f"❌ Server returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to server: {e}")
        return False
    
    # Test 2: Check language switching
    try:
        response = requests.get(f"{base_url}/set_language/ar", timeout=5)
        if response.status_code in [200, 302]:  # 302 is redirect
            print("✅ Language switching works")
        else:
            print(f"❌ Language switching failed: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"⚠️  Language switching test failed: {e}")
    
    # Test 3: Check if login page loads
    try:
        response = requests.get(base_url, timeout=5)
        if "login" in response.text.lower() or "تسجيل الدخول" in response.text:
            print("✅ Login page loads correctly")
        else:
            print("⚠️  Login page content may be incorrect")
    except Exception as e:
        print(f"⚠️  Login page test failed: {e}")
    
    print("\n🎯 Manual Testing Instructions:")
    print("1. Open browser and go to: http://localhost:2121")
    print("2. Login with: admin / admin123")
    print("3. Test the following features:")
    print("   - Dashboard statistics")
    print("   - Add/edit customers")
    print("   - Add/edit products")
    print("   - Create invoices")
    print("   - Download PDF invoices")
    print("   - View reports")
    print("   - Company settings")
    print("   - Language switching (English/Arabic)")
    
    return True

if __name__ == "__main__":
    test_system()
