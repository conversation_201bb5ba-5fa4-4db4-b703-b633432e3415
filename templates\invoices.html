{% extends "base.html" %}

{% block title %}
    {% if current_language == 'ar' %}الفواتير{% else %}Invoices{% endif %} - Billing System
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                {% if current_language == 'ar' %}الفواتير{% else %}Invoices{% endif %}
            </h1>
            <a href="{{ url_for('create_invoice') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                {% if current_language == 'ar' %}إنشاء فاتورة{% else %}Create Invoice{% endif %}
            </a>
        </div>
    </div>
</div>

<!-- Search and Filter Bar -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-5">
                        <input type="text" class="form-control" name="search"
                               value="{{ search or '' }}"
                               placeholder="{% if current_language == 'ar' %}البحث في الفواتير...{% else %}Search invoices...{% endif %}">
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="status">
                            <option value="">
                                {% if current_language == 'ar' %}جميع الحالات{% else %}All Status{% endif %}
                            </option>
                            <option value="draft" {{ 'selected' if status == 'draft' else '' }}>
                                {% if current_language == 'ar' %}مسودة{% else %}Draft{% endif %}
                            </option>
                            <option value="sent" {{ 'selected' if status == 'sent' else '' }}>
                                {% if current_language == 'ar' %}مرسلة{% else %}Sent{% endif %}
                            </option>
                            <option value="paid" {{ 'selected' if status == 'paid' else '' }}>
                                {% if current_language == 'ar' %}مدفوعة{% else %}Paid{% endif %}
                            </option>
                            <option value="cancelled" {{ 'selected' if status == 'cancelled' else '' }}>
                                {% if current_language == 'ar' %}ملغية{% else %}Cancelled{% endif %}
                            </option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                                {% if current_language == 'ar' %}بحث{% else %}Search{% endif %}
                            </button>
                            {% if search or status %}
                            <a href="{{ url_for('invoices') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                                {% if current_language == 'ar' %}إزالة{% else %}Clear{% endif %}
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if invoices.items %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>
                                    {% if current_language == 'ar' %}رقم الفاتورة{% else %}Invoice #{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}العميل{% else %}Customer{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}تاريخ الإصدار{% else %}Issue Date{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}تاريخ الاستحقاق{% else %}Due Date{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}المبلغ{% else %}Amount{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}الحالة{% else %}Status{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}إجراءات{% else %}Actions{% endif %}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in invoices.items %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('view_invoice', id=invoice.id) }}" class="text-decoration-none">
                                        {{ invoice.invoice_number }}
                                    </a>
                                </td>
                                <td>
                                    {% if current_language == 'ar' and invoice.customer.name_ar %}
                                        {{ invoice.customer.name_ar }}
                                    {% else %}
                                        {{ invoice.customer.name_en }}
                                    {% endif %}
                                </td>
                                <td>{{ invoice.issue_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if invoice.due_date %}
                                        {{ invoice.due_date.strftime('%Y-%m-%d') }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>${{ "%.2f"|format(invoice.total_amount) }}</td>
                                <td>
                                    {% if invoice.status == 'paid' %}
                                        <span class="badge bg-success">
                                            {% if current_language == 'ar' %}مدفوعة{% else %}Paid{% endif %}
                                        </span>
                                    {% elif invoice.status == 'sent' %}
                                        <span class="badge bg-info">
                                            {% if current_language == 'ar' %}مرسلة{% else %}Sent{% endif %}
                                        </span>
                                    {% elif invoice.status == 'cancelled' %}
                                        <span class="badge bg-danger">
                                            {% if current_language == 'ar' %}ملغية{% else %}Cancelled{% endif %}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-warning">
                                            {% if current_language == 'ar' %}مسودة{% else %}Draft{% endif %}
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('view_invoice', id=invoice.id) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                            {% if current_language == 'ar' %}عرض{% else %}View{% endif %}
                                        </a>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                    data-bs-toggle="dropdown">
                                                <i class="fas fa-download"></i>
                                                PDF
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" 
                                                       href="{{ url_for('download_invoice_pdf', id=invoice.id, language='en') }}">
                                                        <i class="fas fa-file-pdf"></i> English
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" 
                                                       href="{{ url_for('download_invoice_pdf', id=invoice.id, language='ar') }}">
                                                        <i class="fas fa-file-pdf"></i> العربية
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if invoices.pages > 1 %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if invoices.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('invoices', page=invoices.prev_num) }}">
                                {% if current_language == 'ar' %}السابق{% else %}Previous{% endif %}
                            </a>
                        </li>
                        {% endif %}

                        {% for page_num in invoices.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != invoices.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('invoices', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if invoices.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('invoices', page=invoices.next_num) }}">
                                {% if current_language == 'ar' %}التالي{% else %}Next{% endif %}
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">
                        {% if current_language == 'ar' %}لا توجد فواتير{% else %}No invoices found{% endif %}
                    </h5>
                    <p class="text-muted">
                        {% if current_language == 'ar' %}
                            ابدأ بإنشاء فاتورة جديدة
                        {% else %}
                            Start by creating a new invoice
                        {% endif %}
                    </p>
                    <a href="{{ url_for('create_invoice') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        {% if current_language == 'ar' %}إنشاء فاتورة{% else %}Create Invoice{% endif %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
