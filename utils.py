import os
import arabic_reshaper
from bidi.algorithm import get_display
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from flask import current_app

def reshape_arabic_text(text):
    """Reshape Arabic text for proper display in PDF"""
    if not text:
        return ""
    reshaped_text = arabic_reshaper.reshape(text)
    return get_display(reshaped_text)

def format_currency(amount, currency_symbol="$"):
    """Format currency with proper symbol"""
    return f"{currency_symbol}{amount:,.2f}"

def generate_invoice_number():
    """Generate unique invoice number"""
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    return f"INV-{timestamp}"

def ensure_upload_folders():
    """Ensure upload folders exist"""
    folders = [
        current_app.config['PDF_UPLOAD_FOLDER'],
        current_app.config['LOGO_UPLOAD_FOLDER']
    ]
    for folder in folders:
        if not os.path.exists(folder):
            os.makedirs(folder)

def get_company_settings():
    """Get company settings from database or return defaults"""
    from models import CompanySettings
    settings = CompanySettings.query.first()
    if not settings:
        # Return default settings
        return {
            'company_name_en': current_app.config['COMPANY_NAME_EN'],
            'company_name_ar': current_app.config['COMPANY_NAME_AR'],
            'address_en': current_app.config['COMPANY_ADDRESS_EN'],
            'address_ar': current_app.config['COMPANY_ADDRESS_AR'],
            'phone': current_app.config['COMPANY_PHONE'],
            'email': current_app.config['COMPANY_EMAIL'],
            'logo_path': None
        }
    return {
        'company_name_en': settings.company_name_en or current_app.config['COMPANY_NAME_EN'],
        'company_name_ar': settings.company_name_ar or current_app.config['COMPANY_NAME_AR'],
        'address_en': settings.address_en or current_app.config['COMPANY_ADDRESS_EN'],
        'address_ar': settings.address_ar or current_app.config['COMPANY_ADDRESS_AR'],
        'phone': settings.phone or current_app.config['COMPANY_PHONE'],
        'email': settings.email or current_app.config['COMPANY_EMAIL'],
        'logo_path': settings.logo_path
    }

def create_pdf_invoice(invoice, language='en'):
    """Create PDF invoice using ReportLab"""
    ensure_upload_folders()

    # Create filename
    filename = f"invoice_{invoice.invoice_number}_{language}.pdf"
    filepath = os.path.join(current_app.config['PDF_UPLOAD_FOLDER'], filename)

    # Create PDF document
    doc = SimpleDocTemplate(filepath, pagesize=A4, topMargin=0.5*inch)
    story = []

    # Get styles
    styles = getSampleStyleSheet()

    # Company settings
    company = get_company_settings()

    # Custom styles
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=28,
        spaceAfter=20,
        textColor=colors.darkblue,
        alignment=1 if language == 'ar' else 0
    )

    header_style = ParagraphStyle(
        'HeaderStyle',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=6,
        alignment=1 if language == 'ar' else 0
    )

    # Header section with company info
    header_data = []
    if language == 'ar':
        company_name = reshape_arabic_text(company['company_name_ar'])
        company_address = reshape_arabic_text(company['address_ar'])
        invoice_title = reshape_arabic_text("فاتورة")
    else:
        company_name = company['company_name_en']
        company_address = company['address_en']
        invoice_title = "INVOICE"

    # Company header
    story.append(Paragraph(company_name, title_style))
    story.append(Paragraph(company_address, header_style))
    story.append(Paragraph(f"Phone: {company['phone']} | Email: {company['email']}", header_style))
    story.append(Spacer(1, 20))

    # Invoice title and details
    story.append(Paragraph(invoice_title, title_style))
    story.append(Spacer(1, 15))

    # Invoice and customer info in two columns
    info_data = []
    if language == 'ar':
        info_data = [
            [reshape_arabic_text("رقم الفاتورة:"), invoice.invoice_number,
             reshape_arabic_text("فوترة إلى:"), reshape_arabic_text(invoice.customer.name_ar or invoice.customer.name_en)],
            [reshape_arabic_text("تاريخ الإصدار:"), invoice.issue_date.strftime("%Y-%m-%d"),
             reshape_arabic_text("العنوان:"), reshape_arabic_text(invoice.customer.address_ar or invoice.customer.address_en or "")],
        ]
        if invoice.due_date:
            info_data.append([reshape_arabic_text("تاريخ الاستحقاق:"), invoice.due_date.strftime("%Y-%m-%d"),
                             reshape_arabic_text("الهاتف:"), invoice.customer.phone or ""])
    else:
        info_data = [
            ["Invoice Number:", invoice.invoice_number, "Bill To:", invoice.customer.name_en],
            ["Issue Date:", invoice.issue_date.strftime("%Y-%m-%d"), "Address:", invoice.customer.address_en or ""],
        ]
        if invoice.due_date:
            info_data.append(["Due Date:", invoice.due_date.strftime("%Y-%m-%d"), "Phone:", invoice.customer.phone or ""])

    info_table = Table(info_data, colWidths=[1.5*inch, 2*inch, 1*inch, 2.5*inch])
    info_table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTNAME', (2, 0), (2, -1), 'Helvetica-Bold'),
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
    ]))

    story.append(info_table)
    story.append(Spacer(1, 25))

    # Invoice items table
    items_data = []
    if language == 'ar':
        items_data.append([
            reshape_arabic_text("الوصف"),
            reshape_arabic_text("الكمية"),
            reshape_arabic_text("سعر الوحدة"),
            reshape_arabic_text("الضريبة"),
            reshape_arabic_text("الإجمالي")
        ])
    else:
        items_data.append(["Description", "Qty", "Unit Price", "Tax", "Total"])

    for item in invoice.items:
        if language == 'ar':
            description = reshape_arabic_text(item.description_ar or item.description_en)
        else:
            description = item.description_en

        line_total = item.line_total
        tax_amount = line_total * (item.tax_rate / 100)
        total_with_tax = line_total + tax_amount

        items_data.append([
            description,
            str(item.quantity),
            f"${item.unit_price:.2f}",
            f"{item.tax_rate}%",
            f"${total_with_tax:.2f}"
        ])

    items_table = Table(items_data, colWidths=[3*inch, 0.8*inch, 1*inch, 0.8*inch, 1*inch])
    items_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (1, 0), (-1, -1), 'CENTER'),
        ('ALIGN', (0, 0), (0, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
    ]))

    story.append(items_table)
    story.append(Spacer(1, 20))

    # Totals section
    totals_data = []
    if language == 'ar':
        totals_data = [
            ["", "", "", reshape_arabic_text("المجموع الفرعي:"), f"${invoice.subtotal:.2f}"],
            ["", "", "", reshape_arabic_text("الضريبة:"), f"${invoice.tax_amount:.2f}"],
            ["", "", "", reshape_arabic_text("الإجمالي:"), f"${invoice.total_amount:.2f}"],
        ]
    else:
        totals_data = [
            ["", "", "", "Subtotal:", f"${invoice.subtotal:.2f}"],
            ["", "", "", "Tax:", f"${invoice.tax_amount:.2f}"],
            ["", "", "", "Total:", f"${invoice.total_amount:.2f}"],
        ]

    totals_table = Table(totals_data, colWidths=[3*inch, 0.8*inch, 1*inch, 0.8*inch, 1*inch])
    totals_table.setStyle(TableStyle([
        ('ALIGN', (3, 0), (-1, -1), 'RIGHT'),
        ('FONTNAME', (3, 0), (-1, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (3, 0), (-1, -1), 11),
        ('BACKGROUND', (3, 2), (-1, 2), colors.lightgrey),
        ('FONTSIZE', (3, 2), (-1, 2), 12),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
    ]))

    story.append(totals_table)
    story.append(Spacer(1, 30))

    # Notes section
    if invoice.notes_en or invoice.notes_ar:
        notes_text = invoice.notes_ar if language == 'ar' and invoice.notes_ar else invoice.notes_en
        if notes_text:
            if language == 'ar':
                notes_title = reshape_arabic_text("ملاحظات:")
                notes_content = reshape_arabic_text(notes_text)
            else:
                notes_title = "Notes:"
                notes_content = notes_text

            story.append(Paragraph(notes_title, styles['Heading3']))
            story.append(Paragraph(notes_content, styles['Normal']))
            story.append(Spacer(1, 20))

    # Footer
    footer_text = "Thank you for your business!" if language == 'en' else reshape_arabic_text("شكراً لك على عملك معنا!")
    footer_style = ParagraphStyle(
        'FooterStyle',
        parent=styles['Normal'],
        fontSize=12,
        textColor=colors.grey,
        alignment=1  # Center
    )
    story.append(Paragraph(footer_text, footer_style))

    # Build PDF
    doc.build(story)

    return filepath
