{% extends "base.html" %}

{% block title %}
    {% if current_language == 'ar' %}إنشاء فاتورة{% else %}Create Invoice{% endif %} - Billing System
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                {% if current_language == 'ar' %}إنشاء فاتورة جديدة{% else %}Create New Invoice{% endif %}
            </h1>
            <a href="{{ url_for('invoices') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                {% if current_language == 'ar' %}العودة{% else %}Back{% endif %}
            </a>
        </div>
    </div>
</div>

<form method="POST" id="invoice-form">
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        {% if current_language == 'ar' %}تفاصيل الفاتورة{% else %}Invoice Details{% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="customer_id" class="form-label">
                                {% if current_language == 'ar' %}العميل *{% else %}Customer *{% endif %}
                            </label>
                            <select class="form-select" id="customer_id" name="customer_id" required>
                                <option value="">
                                    {% if current_language == 'ar' %}اختر العميل{% else %}Select Customer{% endif %}
                                </option>
                                {% for customer in customers %}
                                <option value="{{ customer.id }}">
                                    {% if current_language == 'ar' and customer.name_ar %}
                                        {{ customer.name_ar }}
                                    {% else %}
                                        {{ customer.name_en }}
                                    {% endif %}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="issue_date" class="form-label">
                                {% if current_language == 'ar' %}تاريخ الإصدار *{% else %}Issue Date *{% endif %}
                            </label>
                            <input type="date" class="form-control" id="issue_date" name="issue_date"
                                   value="{{ today.strftime('%Y-%m-%d') }}" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="due_date" class="form-label">
                                {% if current_language == 'ar' %}تاريخ الاستحقاق{% else %}Due Date{% endif %}
                            </label>
                            <input type="date" class="form-control" id="due_date" name="due_date">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="notes_en" class="form-label">
                                {% if current_language == 'ar' %}ملاحظات (إنجليزي){% else %}Notes (English){% endif %}
                            </label>
                            <textarea class="form-control" id="notes_en" name="notes_en" rows="3"></textarea>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="notes_ar" class="form-label">
                                {% if current_language == 'ar' %}ملاحظات (عربي){% else %}Notes (Arabic){% endif %}
                            </label>
                            <textarea class="form-control" id="notes_ar" name="notes_ar" rows="3"
                                      {% if current_language == 'ar' %}dir="rtl"{% endif %}></textarea>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Invoice Items -->
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        {% if current_language == 'ar' %}عناصر الفاتورة{% else %}Invoice Items{% endif %}
                    </h5>
                    <button type="button" class="btn btn-sm btn-primary" onclick="addInvoiceItem()">
                        <i class="fas fa-plus"></i>
                        {% if current_language == 'ar' %}إضافة عنصر{% else %}Add Item{% endif %}
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table" id="invoice-items-table">
                            <thead>
                                <tr>
                                    <th>
                                        {% if current_language == 'ar' %}الوصف{% else %}Description{% endif %}
                                    </th>
                                    <th>
                                        {% if current_language == 'ar' %}الكمية{% else %}Quantity{% endif %}
                                    </th>
                                    <th>
                                        {% if current_language == 'ar' %}سعر الوحدة{% else %}Unit Price{% endif %}
                                    </th>
                                    <th>
                                        {% if current_language == 'ar' %}الضريبة (%){% else %}Tax (%){% endif %}
                                    </th>
                                    <th>
                                        {% if current_language == 'ar' %}الإجمالي{% else %}Total{% endif %}
                                    </th>
                                    <th>
                                        {% if current_language == 'ar' %}إجراءات{% else %}Actions{% endif %}
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="invoice-items">
                                <!-- Items will be added here dynamically -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-6 offset-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>
                                        {% if current_language == 'ar' %}المجموع الفرعي:{% else %}Subtotal:{% endif %}
                                    </strong></td>
                                    <td class="text-end"><strong id="subtotal">$0.00</strong></td>
                                </tr>
                                <tr>
                                    <td><strong>
                                        {% if current_language == 'ar' %}الضريبة:{% else %}Tax:{% endif %}
                                    </strong></td>
                                    <td class="text-end"><strong id="tax-total">$0.00</strong></td>
                                </tr>
                                <tr class="table-primary">
                                    <td><strong>
                                        {% if current_language == 'ar' %}الإجمالي:{% else %}Total:{% endif %}
                                    </strong></td>
                                    <td class="text-end"><strong id="grand-total">$0.00</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        {% if current_language == 'ar' %}إجراءات سريعة{% else %}Quick Actions{% endif %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="product-select" class="form-label">
                            {% if current_language == 'ar' %}إضافة منتج:{% else %}Add Product:{% endif %}
                        </label>
                        <select class="form-select" id="product-select">
                            <option value="">
                                {% if current_language == 'ar' %}اختر منتج{% else %}Select Product{% endif %}
                            </option>
                            {% for product in products %}
                            <option value="{{ product.id }}" 
                                    data-name-en="{{ product.name_en }}"
                                    data-name-ar="{{ product.name_ar or '' }}"
                                    data-price="{{ product.price }}"
                                    data-tax="{{ product.tax_rate }}">
                                {% if current_language == 'ar' and product.name_ar %}
                                    {{ product.name_ar }}
                                {% else %}
                                    {{ product.name_en }}
                                {% endif %}
                                - ${{ "%.2f"|format(product.price) }}
                            </option>
                            {% endfor %}
                        </select>
                        <button type="button" class="btn btn-sm btn-outline-primary mt-2 w-100" 
                                onclick="addProductToInvoice()">
                            {% if current_language == 'ar' %}إضافة للفاتورة{% else %}Add to Invoice{% endif %}
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        {% if current_language == 'ar' %}حفظ الفاتورة{% else %}Save Invoice{% endif %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            {% if current_language == 'ar' %}حفظ الفاتورة{% else %}Save Invoice{% endif %}
                        </button>
                        <a href="{{ url_for('invoices') }}" class="btn btn-secondary">
                            {% if current_language == 'ar' %}إلغاء{% else %}Cancel{% endif %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
let itemCounter = 0;

function addInvoiceItem() {
    const tbody = document.getElementById('invoice-items');
    const row = document.createElement('tr');
    row.id = `item-${itemCounter}`;

    row.innerHTML = `
        <td>
            <input type="text" class="form-control form-control-sm"
                   name="item_description_en_${itemCounter}"
                   placeholder="{{ 'الوصف' if current_language == 'ar' else 'Description' }}" required>
        </td>
        <td>
            <input type="number" class="form-control form-control-sm"
                   name="item_quantity_${itemCounter}"
                   step="0.01" min="0.01" value="1"
                   onchange="calculateItemTotal(${itemCounter})" required>
        </td>
        <td>
            <input type="number" class="form-control form-control-sm"
                   name="item_price_${itemCounter}"
                   step="0.01" min="0"
                   onchange="calculateItemTotal(${itemCounter})" required>
        </td>
        <td>
            <input type="number" class="form-control form-control-sm"
                   name="item_tax_${itemCounter}"
                   step="0.01" min="0" max="100" value="0"
                   onchange="calculateItemTotal(${itemCounter})">
        </td>
        <td>
            <span id="item-total-${itemCounter}">$0.00</span>
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-outline-danger"
                    onclick="removeInvoiceItem(${itemCounter})">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;

    tbody.appendChild(row);
    itemCounter++;
    calculateTotals();
}

function removeInvoiceItem(id) {
    const row = document.getElementById(`item-${id}`);
    if (row) {
        row.remove();
        calculateTotals();
    }
}

function calculateItemTotal(id) {
    const quantity = parseFloat(document.querySelector(`input[name="item_quantity_${id}"]`).value) || 0;
    const price = parseFloat(document.querySelector(`input[name="item_price_${id}"]`).value) || 0;
    const taxRate = parseFloat(document.querySelector(`input[name="item_tax_${id}"]`).value) || 0;

    const subtotal = quantity * price;
    const tax = subtotal * (taxRate / 100);
    const total = subtotal + tax;

    document.getElementById(`item-total-${id}`).textContent = '$' + total.toFixed(2);
    calculateTotals();
}

function calculateTotals() {
    let subtotal = 0;
    let taxTotal = 0;

    for (let i = 0; i < itemCounter; i++) {
        const quantityInput = document.querySelector(`input[name="item_quantity_${i}"]`);
        const priceInput = document.querySelector(`input[name="item_price_${i}"]`);
        const taxInput = document.querySelector(`input[name="item_tax_${i}"]`);

        if (quantityInput && priceInput && taxInput) {
            const quantity = parseFloat(quantityInput.value) || 0;
            const price = parseFloat(priceInput.value) || 0;
            const taxRate = parseFloat(taxInput.value) || 0;

            const itemSubtotal = quantity * price;
            const itemTax = itemSubtotal * (taxRate / 100);

            subtotal += itemSubtotal;
            taxTotal += itemTax;
        }
    }

    const grandTotal = subtotal + taxTotal;

    document.getElementById('subtotal').textContent = '$' + subtotal.toFixed(2);
    document.getElementById('tax-total').textContent = '$' + taxTotal.toFixed(2);
    document.getElementById('grand-total').textContent = '$' + grandTotal.toFixed(2);
}

function addProductToInvoice() {
    const select = document.getElementById('product-select');
    const selectedOption = select.options[select.selectedIndex];

    if (selectedOption.value) {
        addInvoiceItem();

        const currentRow = itemCounter - 1;
        const nameEn = selectedOption.getAttribute('data-name-en');
        const nameAr = selectedOption.getAttribute('data-name-ar');
        const price = selectedOption.getAttribute('data-price');
        const tax = selectedOption.getAttribute('data-tax');

        // Fill in the product details
        const descriptionInput = document.querySelector(`input[name="item_description_en_${currentRow}"]`);
        const priceInput = document.querySelector(`input[name="item_price_${currentRow}"]`);
        const taxInput = document.querySelector(`input[name="item_tax_${currentRow}"]`);

        if (descriptionInput) {
            descriptionInput.value = "{{ current_language }}" === 'ar' && nameAr ? nameAr : nameEn;
        }
        if (priceInput) {
            priceInput.value = price;
        }
        if (taxInput) {
            taxInput.value = tax;
        }

        calculateItemTotal(currentRow);
        select.selectedIndex = 0;
    }
}

// Form submission
document.getElementById('invoice-form').addEventListener('submit', function(e) {
    e.preventDefault();

    // Collect all items data
    const items = [];
    for (let i = 0; i < itemCounter; i++) {
        const descriptionInput = document.querySelector(`input[name="item_description_en_${i}"]`);
        const quantityInput = document.querySelector(`input[name="item_quantity_${i}"]`);
        const priceInput = document.querySelector(`input[name="item_price_${i}"]`);
        const taxInput = document.querySelector(`input[name="item_tax_${i}"]`);

        if (descriptionInput && quantityInput && priceInput && taxInput) {
            items.push({
                description_en: descriptionInput.value,
                quantity: quantityInput.value,
                unit_price: priceInput.value,
                tax_rate: taxInput.value
            });
        }
    }

    // Add items to form as hidden inputs
    items.forEach((item, index) => {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'items';
        hiddenInput.value = JSON.stringify(item);
        this.appendChild(hiddenInput);
    });

    this.submit();
});

// Add initial item
addInvoiceItem();
</script>
{% endblock %}
