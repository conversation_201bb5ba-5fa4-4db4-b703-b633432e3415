{% extends "base.html" %}

{% block title %}
    {% if current_language == 'ar' %}التقارير{% else %}Reports{% endif %} - Billing System
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                {% if current_language == 'ar' %}التقارير المالية{% else %}Financial Reports{% endif %}
            </h1>
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                {% if current_language == 'ar' %}العودة{% else %}Back{% endif %}
            </a>
        </div>
    </div>
</div>

<!-- Date Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    {% if current_language == 'ar' %}تصفية التقرير{% else %}Filter Report{% endif %}
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="start_date" class="form-label">
                            {% if current_language == 'ar' %}من تاريخ{% else %}From Date{% endif %}
                        </label>
                        <input type="date" class="form-control" id="start_date" name="start_date" 
                               value="{{ request.args.get('start_date', '') }}">
                    </div>
                    <div class="col-md-4">
                        <label for="end_date" class="form-label">
                            {% if current_language == 'ar' %}إلى تاريخ{% else %}To Date{% endif %}
                        </label>
                        <input type="date" class="form-control" id="end_date" name="end_date" 
                               value="{{ request.args.get('end_date', '') }}">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter"></i>
                                {% if current_language == 'ar' %}تطبيق{% else %}Apply{% endif %}
                            </button>
                            <a href="{{ url_for('reports') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                                {% if current_language == 'ar' %}إزالة{% else %}Clear{% endif %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summary Statistics -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>${{ "%.2f"|format(report.total_revenue) }}</h4>
                        <p class="mb-0">
                            {% if current_language == 'ar' %}إجمالي الإيرادات{% else %}Total Revenue{% endif %}
                        </p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>${{ "%.2f"|format(report.total_tax) }}</h4>
                        <p class="mb-0">
                            {% if current_language == 'ar' %}إجمالي الضرائب{% else %}Total Tax{% endif %}
                        </p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-percentage fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ report.invoice_count }}</h4>
                        <p class="mb-0">
                            {% if current_language == 'ar' %}عدد الفواتير{% else %}Invoice Count{% endif %}
                        </p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-invoice fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>${{ "%.2f"|format(report.total_revenue / report.invoice_count if report.invoice_count > 0 else 0) }}</h4>
                        <p class="mb-0">
                            {% if current_language == 'ar' %}متوسط الفاتورة{% else %}Average Invoice{% endif %}
                        </p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Breakdown -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    {% if current_language == 'ar' %}الفواتير حسب الحالة{% else %}Invoices by Status{% endif %}
                </h6>
            </div>
            <div class="card-body">
                {% if report.status_stats %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>
                                    {% if current_language == 'ar' %}الحالة{% else %}Status{% endif %}
                                </th>
                                <th class="text-center">
                                    {% if current_language == 'ar' %}العدد{% else %}Count{% endif %}
                                </th>
                                <th class="text-end">
                                    {% if current_language == 'ar' %}المبلغ{% else %}Amount{% endif %}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for status, data in report.status_stats.items() %}
                            <tr>
                                <td>
                                    {% if status == 'paid' %}
                                        <span class="badge bg-success">
                                            {% if current_language == 'ar' %}مدفوعة{% else %}Paid{% endif %}
                                        </span>
                                    {% elif status == 'sent' %}
                                        <span class="badge bg-info">
                                            {% if current_language == 'ar' %}مرسلة{% else %}Sent{% endif %}
                                        </span>
                                    {% elif status == 'cancelled' %}
                                        <span class="badge bg-danger">
                                            {% if current_language == 'ar' %}ملغية{% else %}Cancelled{% endif %}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-warning">
                                            {% if current_language == 'ar' %}مسودة{% else %}Draft{% endif %}
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="text-center">{{ data.count }}</td>
                                <td class="text-end">${{ "%.2f"|format(data.amount) }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">
                    {% if current_language == 'ar' %}لا توجد بيانات{% else %}No data available{% endif %}
                </p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    {% if current_language == 'ar' %}أفضل العملاء{% else %}Top Customers{% endif %}
                </h6>
            </div>
            <div class="card-body">
                {% if report.customer_stats %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>
                                    {% if current_language == 'ar' %}العميل{% else %}Customer{% endif %}
                                </th>
                                <th class="text-center">
                                    {% if current_language == 'ar' %}الفواتير{% else %}Invoices{% endif %}
                                </th>
                                <th class="text-end">
                                    {% if current_language == 'ar' %}المبلغ{% else %}Amount{% endif %}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer, data in report.customer_stats.items() %}
                            <tr>
                                <td>{{ customer }}</td>
                                <td class="text-center">{{ data.count }}</td>
                                <td class="text-end">${{ "%.2f"|format(data.amount) }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">
                    {% if current_language == 'ar' %}لا توجد بيانات{% else %}No data available{% endif %}
                </p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Detailed Invoice List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    {% if current_language == 'ar' %}تفاصيل الفواتير{% else %}Invoice Details{% endif %}
                </h6>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="exportToCSV()">
                    <i class="fas fa-download"></i>
                    {% if current_language == 'ar' %}تصدير CSV{% else %}Export CSV{% endif %}
                </button>
            </div>
            <div class="card-body">
                {% if report.invoices %}
                <div class="table-responsive">
                    <table class="table table-striped" id="invoices-table">
                        <thead>
                            <tr>
                                <th>
                                    {% if current_language == 'ar' %}رقم الفاتورة{% else %}Invoice #{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}العميل{% else %}Customer{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}التاريخ{% else %}Date{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}المبلغ{% else %}Amount{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}الضريبة{% else %}Tax{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}الإجمالي{% else %}Total{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}الحالة{% else %}Status{% endif %}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in report.invoices %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('view_invoice', id=invoice.id) }}" class="text-decoration-none">
                                        {{ invoice.invoice_number }}
                                    </a>
                                </td>
                                <td>{{ invoice.customer.name_en }}</td>
                                <td>{{ invoice.issue_date.strftime('%Y-%m-%d') }}</td>
                                <td>${{ "%.2f"|format(invoice.subtotal) }}</td>
                                <td>${{ "%.2f"|format(invoice.tax_amount) }}</td>
                                <td><strong>${{ "%.2f"|format(invoice.total_amount) }}</strong></td>
                                <td>
                                    {% if invoice.status == 'paid' %}
                                        <span class="badge bg-success">
                                            {% if current_language == 'ar' %}مدفوعة{% else %}Paid{% endif %}
                                        </span>
                                    {% elif invoice.status == 'sent' %}
                                        <span class="badge bg-info">
                                            {% if current_language == 'ar' %}مرسلة{% else %}Sent{% endif %}
                                        </span>
                                    {% elif invoice.status == 'cancelled' %}
                                        <span class="badge bg-danger">
                                            {% if current_language == 'ar' %}ملغية{% else %}Cancelled{% endif %}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-warning">
                                            {% if current_language == 'ar' %}مسودة{% else %}Draft{% endif %}
                                        </span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">
                    {% if current_language == 'ar' %}لا توجد فواتير في هذه الفترة{% else %}No invoices found for this period{% endif %}
                </p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function exportToCSV() {
    const table = document.getElementById('invoices-table');
    const rows = table.querySelectorAll('tr');
    let csv = [];
    
    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const cols = row.querySelectorAll('td, th');
        let csvRow = [];
        
        for (let j = 0; j < cols.length; j++) {
            csvRow.push('"' + cols[j].innerText.replace(/"/g, '""') + '"');
        }
        
        csv.push(csvRow.join(','));
    }
    
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'invoices_report.csv';
    a.click();
    window.URL.revokeObjectURL(url);
}
</script>
{% endblock %}
