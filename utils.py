import os
import arabic_reshaper
from bidi.algorithm import get_display
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from flask import current_app

def reshape_arabic_text(text):
    """Reshape Arabic text for proper display in PDF"""
    if not text:
        return ""
    reshaped_text = arabic_reshaper.reshape(text)
    return get_display(reshaped_text)

def format_currency(amount, currency_symbol="$"):
    """Format currency with proper symbol"""
    return f"{currency_symbol}{amount:,.2f}"

def generate_invoice_number():
    """Generate unique invoice number"""
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    return f"INV-{timestamp}"

def ensure_upload_folders():
    """Ensure upload folders exist"""
    folders = [
        current_app.config['PDF_UPLOAD_FOLDER'],
        current_app.config['LOGO_UPLOAD_FOLDER']
    ]
    for folder in folders:
        if not os.path.exists(folder):
            os.makedirs(folder)

def get_company_settings():
    """Get company settings from database or return defaults"""
    from models import CompanySettings
    settings = CompanySettings.query.first()
    if not settings:
        # Return default settings
        return {
            'company_name_en': current_app.config['COMPANY_NAME_EN'],
            'company_name_ar': current_app.config['COMPANY_NAME_AR'],
            'address_en': current_app.config['COMPANY_ADDRESS_EN'],
            'address_ar': current_app.config['COMPANY_ADDRESS_AR'],
            'phone': current_app.config['COMPANY_PHONE'],
            'email': current_app.config['COMPANY_EMAIL'],
            'logo_path': None
        }
    return {
        'company_name_en': settings.company_name_en or current_app.config['COMPANY_NAME_EN'],
        'company_name_ar': settings.company_name_ar or current_app.config['COMPANY_NAME_AR'],
        'address_en': settings.address_en or current_app.config['COMPANY_ADDRESS_EN'],
        'address_ar': settings.address_ar or current_app.config['COMPANY_ADDRESS_AR'],
        'phone': settings.phone or current_app.config['COMPANY_PHONE'],
        'email': settings.email or current_app.config['COMPANY_EMAIL'],
        'logo_path': settings.logo_path
    }

def create_pdf_invoice(invoice, language='en'):
    """Create PDF invoice using ReportLab"""
    ensure_upload_folders()
    
    # Create filename
    filename = f"invoice_{invoice.invoice_number}_{language}.pdf"
    filepath = os.path.join(current_app.config['PDF_UPLOAD_FOLDER'], filename)
    
    # Create PDF document
    doc = SimpleDocTemplate(filepath, pagesize=A4)
    story = []
    
    # Get styles
    styles = getSampleStyleSheet()
    
    # Company settings
    company = get_company_settings()
    
    # Title style
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=24,
        spaceAfter=30,
        alignment=1 if language == 'ar' else 0  # Right align for Arabic
    )
    
    # Add company logo if exists
    if company['logo_path'] and os.path.exists(company['logo_path']):
        logo = Image(company['logo_path'], width=2*inch, height=1*inch)
        story.append(logo)
        story.append(Spacer(1, 12))
    
    # Company information
    if language == 'ar':
        company_name = reshape_arabic_text(company['company_name_ar'])
        company_address = reshape_arabic_text(company['address_ar'])
        invoice_title = reshape_arabic_text("فاتورة")
    else:
        company_name = company['company_name_en']
        company_address = company['address_en']
        invoice_title = "INVOICE"
    
    # Add company info
    story.append(Paragraph(company_name, title_style))
    story.append(Paragraph(company_address, styles['Normal']))
    story.append(Paragraph(f"Phone: {company['phone']}", styles['Normal']))
    story.append(Paragraph(f"Email: {company['email']}", styles['Normal']))
    story.append(Spacer(1, 20))
    
    # Invoice title
    story.append(Paragraph(invoice_title, title_style))
    story.append(Spacer(1, 20))
    
    # Invoice details table
    invoice_data = []
    if language == 'ar':
        invoice_data.append([reshape_arabic_text("رقم الفاتورة:"), invoice.invoice_number])
        invoice_data.append([reshape_arabic_text("تاريخ الإصدار:"), invoice.issue_date.strftime("%Y-%m-%d")])
        if invoice.due_date:
            invoice_data.append([reshape_arabic_text("تاريخ الاستحقاق:"), invoice.due_date.strftime("%Y-%m-%d")])
    else:
        invoice_data.append(["Invoice Number:", invoice.invoice_number])
        invoice_data.append(["Issue Date:", invoice.issue_date.strftime("%Y-%m-%d")])
        if invoice.due_date:
            invoice_data.append(["Due Date:", invoice.due_date.strftime("%Y-%m-%d")])
    
    invoice_table = Table(invoice_data, colWidths=[2*inch, 3*inch])
    invoice_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
    ]))
    
    story.append(invoice_table)
    story.append(Spacer(1, 20))
    
    # Customer information
    if language == 'ar':
        customer_title = reshape_arabic_text("معلومات العميل:")
        customer_name = reshape_arabic_text(invoice.customer.name_ar or invoice.customer.name_en)
        customer_address = reshape_arabic_text(invoice.customer.address_ar or invoice.customer.address_en or "")
    else:
        customer_title = "Bill To:"
        customer_name = invoice.customer.name_en
        customer_address = invoice.customer.address_en or ""
    
    story.append(Paragraph(customer_title, styles['Heading2']))
    story.append(Paragraph(customer_name, styles['Normal']))
    if customer_address:
        story.append(Paragraph(customer_address, styles['Normal']))
    if invoice.customer.phone:
        story.append(Paragraph(f"Phone: {invoice.customer.phone}", styles['Normal']))
    if invoice.customer.email:
        story.append(Paragraph(f"Email: {invoice.customer.email}", styles['Normal']))
    
    story.append(Spacer(1, 20))
    
    # Build PDF
    doc.build(story)
    
    return filepath
