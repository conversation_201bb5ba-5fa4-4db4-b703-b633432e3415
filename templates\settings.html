{% extends "base.html" %}

{% block title %}
    {% if current_language == 'ar' %}إعدادات الشركة{% else %}Company Settings{% endif %} - Billing System
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                {% if current_language == 'ar' %}إعدادات الشركة{% else %}Company Settings{% endif %}
            </h1>
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                {% if current_language == 'ar' %}العودة للوحة التحكم{% else %}Back to Dashboard{% endif %}
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    {% if current_language == 'ar' %}معلومات الشركة{% else %}Company Information{% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="company_name_en" class="form-label">
                                {% if current_language == 'ar' %}اسم الشركة (إنجليزي){% else %}Company Name (English){% endif %}
                            </label>
                            <input type="text" class="form-control" id="company_name_en" name="company_name_en" 
                                   value="{{ settings.company_name_en if settings else '' }}">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="company_name_ar" class="form-label">
                                {% if current_language == 'ar' %}اسم الشركة (عربي){% else %}Company Name (Arabic){% endif %}
                            </label>
                            <input type="text" class="form-control" id="company_name_ar" name="company_name_ar" 
                                   value="{{ settings.company_name_ar if settings else '' }}"
                                   {% if current_language == 'ar' %}dir="rtl"{% endif %}>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="address_en" class="form-label">
                                {% if current_language == 'ar' %}العنوان (إنجليزي){% else %}Address (English){% endif %}
                            </label>
                            <textarea class="form-control" id="address_en" name="address_en" rows="3">{{ settings.address_en if settings else '' }}</textarea>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="address_ar" class="form-label">
                                {% if current_language == 'ar' %}العنوان (عربي){% else %}Address (Arabic){% endif %}
                            </label>
                            <textarea class="form-control" id="address_ar" name="address_ar" rows="3"
                                      {% if current_language == 'ar' %}dir="rtl"{% endif %}>{{ settings.address_ar if settings else '' }}</textarea>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">
                                {% if current_language == 'ar' %}رقم الهاتف{% else %}Phone Number{% endif %}
                            </label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="{{ settings.phone if settings else '' }}">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                {% if current_language == 'ar' %}البريد الإلكتروني{% else %}Email{% endif %}
                            </label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ settings.email if settings else '' }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="website" class="form-label">
                                {% if current_language == 'ar' %}الموقع الإلكتروني{% else %}Website{% endif %}
                            </label>
                            <input type="url" class="form-control" id="website" name="website" 
                                   value="{{ settings.website if settings else '' }}">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="tax_number" class="form-label">
                                {% if current_language == 'ar' %}الرقم الضريبي{% else %}Tax Number{% endif %}
                            </label>
                            <input type="text" class="form-control" id="tax_number" name="tax_number" 
                                   value="{{ settings.tax_number if settings else '' }}">
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                            {% if current_language == 'ar' %}إلغاء{% else %}Cancel{% endif %}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            {% if current_language == 'ar' %}حفظ الإعدادات{% else %}Save Settings{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    {% if current_language == 'ar' %}شعار الشركة{% else %}Company Logo{% endif %}
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    {% if settings and settings.logo_path %}
                    <img src="{{ url_for('static', filename='logos/' + settings.logo_path.split('/')[-1]) }}" 
                         alt="Company Logo" class="img-fluid mb-3" style="max-height: 150px;">
                    {% else %}
                    <div class="bg-light p-4 mb-3 rounded">
                        <i class="fas fa-image fa-3x text-muted"></i>
                        <p class="text-muted mt-2">
                            {% if current_language == 'ar' %}لا يوجد شعار{% else %}No logo uploaded{% endif %}
                        </p>
                    </div>
                    {% endif %}
                    
                    <button type="button" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-upload"></i>
                        {% if current_language == 'ar' %}رفع شعار{% else %}Upload Logo{% endif %}
                    </button>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    {% if current_language == 'ar' %}نصائح{% else %}Tips{% endif %}
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-info-circle text-primary me-2"></i>
                        {% if current_language == 'ar' %}
                            هذه المعلومات ستظهر في الفواتير
                        {% else %}
                            This information will appear on invoices
                        {% endif %}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-globe text-info me-2"></i>
                        {% if current_language == 'ar' %}
                            أضف المعلومات بالعربية للفواتير العربية
                        {% else %}
                            Add Arabic information for Arabic invoices
                        {% endif %}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-image text-success me-2"></i>
                        {% if current_language == 'ar' %}
                            الشعار المثالي: 300x150 بكسل
                        {% else %}
                            Ideal logo size: 300x150 pixels
                        {% endif %}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-file-pdf text-warning me-2"></i>
                        {% if current_language == 'ar' %}
                            التغييرات ستطبق على الفواتير الجديدة
                        {% else %}
                            Changes will apply to new invoices
                        {% endif %}
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    {% if current_language == 'ar' %}إحصائيات سريعة{% else %}Quick Stats{% endif %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ Customer.query.count() }}</h4>
                        <small class="text-muted">
                            {% if current_language == 'ar' %}عملاء{% else %}Customers{% endif %}
                        </small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ Invoice.query.count() }}</h4>
                        <small class="text-muted">
                            {% if current_language == 'ar' %}فواتير{% else %}Invoices{% endif %}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
