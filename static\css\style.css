/* Arabic Language Support */
[dir="rtl"] {
    font-family: 'Noto Sans Arabic', Arial, sans-serif;
}

[dir="rtl"] .navbar-nav {
    flex-direction: row-reverse;
}

[dir="rtl"] .navbar-brand {
    margin-right: 0;
    margin-left: 1rem;
}

[dir="rtl"] .dropdown-menu {
    right: 0;
    left: auto;
}

/* General Styles */
body {
    background-color: #f8f9fa;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    border-bottom: none;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
}

.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    background-color: #f8f9fa;
}

/* Dashboard Cards */
.card.bg-primary,
.card.bg-success,
.card.bg-info,
.card.bg-warning {
    border-radius: 15px;
    transition: transform 0.2s;
}

.card.bg-primary:hover,
.card.bg-success:hover,
.card.bg-info:hover,
.card.bg-warning:hover {
    transform: translateY(-5px);
}

/* Form Styles */
.form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 0.75rem;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Invoice Styles */
.invoice-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px 10px 0 0;
}

.invoice-details {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.invoice-table {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
}

.invoice-total {
    background-color: #e9ecef;
    font-weight: bold;
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .container-fluid {
        margin: 0;
        padding: 0;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* Arabic Text Direction */
[dir="rtl"] .text-start {
    text-align: right !important;
}

[dir="rtl"] .text-end {
    text-align: left !important;
}

[dir="rtl"] .me-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

[dir="rtl"] .ms-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-group .btn {
        font-size: 0.875rem;
    }
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Alert Styles */
.alert {
    border-radius: 8px;
    border: none;
}

/* Badge Styles */
.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
    border-radius: 6px;
}

/* Custom Colors */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #f39c12 0%, #f7dc6f 100%);
}
