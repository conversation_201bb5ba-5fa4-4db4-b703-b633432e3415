{% extends "base.html" %}

{% block title %}
    {% if current_language == 'ar' %}العملاء{% else %}Customers{% endif %} - Billing System
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                {% if current_language == 'ar' %}العملاء{% else %}Customers{% endif %}
            </h1>
            <a href="{{ url_for('add_customer') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                {% if current_language == 'ar' %}إضافة عميل{% else %}Add Customer{% endif %}
            </a>
        </div>
    </div>
</div>

<!-- Search Bar -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-8">
                        <input type="text" class="form-control" name="search"
                               value="{{ search or '' }}"
                               placeholder="{% if current_language == 'ar' %}البحث في العملاء...{% else %}Search customers...{% endif %}">
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                                {% if current_language == 'ar' %}بحث{% else %}Search{% endif %}
                            </button>
                            {% if search %}
                            <a href="{{ url_for('customers') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                                {% if current_language == 'ar' %}إزالة{% else %}Clear{% endif %}
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if customers.items %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>
                                    {% if current_language == 'ar' %}الاسم{% else %}Name{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}البريد الإلكتروني{% else %}Email{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}الهاتف{% else %}Phone{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}تاريخ الإنشاء{% else %}Created{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}إجراءات{% else %}Actions{% endif %}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in customers.items %}
                            <tr>
                                <td>
                                    {% if current_language == 'ar' and customer.name_ar %}
                                        {{ customer.name_ar }}
                                    {% else %}
                                        {{ customer.name_en }}
                                    {% endif %}
                                </td>
                                <td>{{ customer.email or '-' }}</td>
                                <td>{{ customer.phone or '-' }}</td>
                                <td>{{ customer.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('edit_customer', id=customer.id) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                            {% if current_language == 'ar' %}تعديل{% else %}Edit{% endif %}
                                        </a>
                                        <form method="POST" action="{{ url_for('delete_customer', id=customer.id) }}"
                                              style="display: inline;" onsubmit="return confirmDelete()">
                                            <button type="submit" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                                {% if current_language == 'ar' %}حذف{% else %}Delete{% endif %}
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if customers.pages > 1 %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if customers.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('customers', page=customers.prev_num) }}">
                                {% if current_language == 'ar' %}السابق{% else %}Previous{% endif %}
                            </a>
                        </li>
                        {% endif %}

                        {% for page_num in customers.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != customers.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('customers', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if customers.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('customers', page=customers.next_num) }}">
                                {% if current_language == 'ar' %}التالي{% else %}Next{% endif %}
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">
                        {% if current_language == 'ar' %}لا يوجد عملاء{% else %}No customers found{% endif %}
                    </h5>
                    <p class="text-muted">
                        {% if current_language == 'ar' %}
                            ابدأ بإضافة عميل جديد
                        {% else %}
                            Start by adding a new customer
                        {% endif %}
                    </p>
                    <a href="{{ url_for('add_customer') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        {% if current_language == 'ar' %}إضافة عميل{% else %}Add Customer{% endif %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete() {
    const message = "{{ 'هل أنت متأكد من حذف هذا العميل؟' if current_language == 'ar' else 'Are you sure you want to delete this customer?' }}";
    return confirm(message);
}
</script>
{% endblock %}
