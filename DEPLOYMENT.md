# دليل النشر - نظام الفواتير الثنائي اللغة
# Deployment Guide - Bilingual Billing System

## 🚀 نشر النظام في بيئة الإنتاج / Production Deployment

### 1. متطل<PERSON>ات الخادم / Server Requirements

#### الحد الأدنى / Minimum Requirements
- **المعالج / CPU**: 1 Core
- **الذاكرة / RAM**: 512 MB
- **التخزين / Storage**: 1 GB
- **نظام التشغيل / OS**: Linux/Windows/macOS
- **Python**: 3.7+

#### الموصى به / Recommended
- **المعالج / CPU**: 2+ Cores
- **الذاكرة / RAM**: 2+ GB
- **التخزين / Storage**: 10+ GB SSD
- **قاعدة البيانات / Database**: PostgreSQL/MySQL

### 2. إعداد البيئة / Environment Setup

#### تثبيت Python والمتطلبات / Install Python & Requirements
```bash
# تحديث النظام / Update system
sudo apt update && sudo apt upgrade -y

# تثبيت Python / Install Python
sudo apt install python3 python3-pip python3-venv -y

# إنشاء بيئة افتراضية / Create virtual environment
python3 -m venv billing_env
source billing_env/bin/activate

# تثبيت المتطلبات / Install requirements
pip install -r requirements.txt
```

#### إعداد قاعدة البيانات / Database Setup

##### SQLite (للاختبار / For Testing)
```bash
# تهيئة قاعدة البيانات / Initialize database
python init_db.py
```

##### PostgreSQL (للإنتاج / For Production)
```bash
# تثبيت PostgreSQL / Install PostgreSQL
sudo apt install postgresql postgresql-contrib -y

# إنشاء قاعدة بيانات / Create database
sudo -u postgres createdb billing_system
sudo -u postgres createuser billing_user
sudo -u postgres psql -c "ALTER USER billing_user PASSWORD 'secure_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE billing_system TO billing_user;"

# تحديث إعدادات الاتصال / Update connection settings
export DATABASE_URL="postgresql://billing_user:secure_password@localhost/billing_system"
```

### 3. إعدادات الأمان / Security Configuration

#### متغيرات البيئة / Environment Variables
```bash
# إنشاء ملف البيئة / Create environment file
cat > .env << EOF
SECRET_KEY=your-very-secure-secret-key-here
DATABASE_URL=postgresql://billing_user:secure_password@localhost/billing_system
FLASK_ENV=production
FLASK_DEBUG=False
EOF
```

#### تحديث كلمة مرور المدير / Update Admin Password
```python
# تشغيل Python / Run Python
python3 -c "
from app import app
from models import db, User
with app.app_context():
    admin = User.query.filter_by(username='admin').first()
    admin.set_password('new_secure_password')
    db.session.commit()
    print('Admin password updated')
"
```

### 4. إعداد خادم الويب / Web Server Setup

#### استخدام Gunicorn / Using Gunicorn
```bash
# تثبيت Gunicorn / Install Gunicorn
pip install gunicorn

# إنشاء ملف التشغيل / Create run script
cat > gunicorn_config.py << EOF
bind = "0.0.0.0:8000"
workers = 2
worker_class = "sync"
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
EOF

# تشغيل التطبيق / Run application
gunicorn -c gunicorn_config.py app:app
```

#### إعداد Nginx / Nginx Setup
```bash
# تثبيت Nginx / Install Nginx
sudo apt install nginx -y

# إنشاء إعدادات الموقع / Create site configuration
sudo cat > /etc/nginx/sites-available/billing << EOF
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    location /static {
        alias /path/to/your/app/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# تفعيل الموقع / Enable site
sudo ln -s /etc/nginx/sites-available/billing /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 5. إعداد SSL / SSL Setup

#### استخدام Let's Encrypt / Using Let's Encrypt
```bash
# تثبيت Certbot / Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# الحصول على شهادة SSL / Get SSL certificate
sudo certbot --nginx -d your-domain.com

# تجديد تلقائي / Auto renewal
sudo crontab -e
# أضف السطر التالي / Add this line:
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### 6. إعداد الخدمة / Service Setup

#### إنشاء خدمة systemd / Create systemd Service
```bash
sudo cat > /etc/systemd/system/billing.service << EOF
[Unit]
Description=Billing System
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/path/to/your/app
Environment=PATH=/path/to/your/app/billing_env/bin
ExecStart=/path/to/your/app/billing_env/bin/gunicorn -c gunicorn_config.py app:app
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# تفعيل الخدمة / Enable service
sudo systemctl daemon-reload
sudo systemctl enable billing
sudo systemctl start billing
sudo systemctl status billing
```

### 7. النسخ الاحتياطي / Backup Setup

#### نسخ احتياطي تلقائي / Automatic Backup
```bash
# إنشاء سكريبت النسخ الاحتياطي / Create backup script
cat > backup.sh << EOF
#!/bin/bash
DATE=\$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/billing"
APP_DIR="/path/to/your/app"

mkdir -p \$BACKUP_DIR

# نسخ قاعدة البيانات / Backup database
if [ -f "\$APP_DIR/billing_system.db" ]; then
    cp "\$APP_DIR/billing_system.db" "\$BACKUP_DIR/billing_system_\$DATE.db"
fi

# نسخ الملفات المرفوعة / Backup uploaded files
tar -czf "\$BACKUP_DIR/static_files_\$DATE.tar.gz" -C "\$APP_DIR" static/

# حذف النسخ القديمة (أكثر من 30 يوم) / Delete old backups (older than 30 days)
find \$BACKUP_DIR -name "*.db" -mtime +30 -delete
find \$BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: \$DATE"
EOF

chmod +x backup.sh

# إضافة للمهام المجدولة / Add to cron
crontab -e
# أضف السطر التالي للنسخ الاحتياطي اليومي / Add this line for daily backup:
# 0 2 * * * /path/to/backup.sh
```

### 8. المراقبة / Monitoring

#### مراقبة الأداء / Performance Monitoring
```bash
# تثبيت htop / Install htop
sudo apt install htop -y

# مراقبة السجلات / Monitor logs
sudo journalctl -u billing -f

# مراقبة استخدام الموارد / Monitor resource usage
htop
```

#### تنبيهات البريد الإلكتروني / Email Alerts
```bash
# تثبيت mailutils / Install mailutils
sudo apt install mailutils -y

# إنشاء سكريبت المراقبة / Create monitoring script
cat > monitor.sh << EOF
#!/bin/bash
if ! systemctl is-active --quiet billing; then
    echo "Billing system is down!" | mail -s "Alert: Billing System Down" <EMAIL>
fi
EOF

chmod +x monitor.sh

# إضافة للمهام المجدولة / Add to cron (every 5 minutes)
# */5 * * * * /path/to/monitor.sh
```

### 9. التحديثات / Updates

#### تحديث النظام / System Updates
```bash
# إيقاف الخدمة / Stop service
sudo systemctl stop billing

# تحديث الكود / Update code
git pull origin main

# تحديث المتطلبات / Update requirements
source billing_env/bin/activate
pip install -r requirements.txt

# تحديث قاعدة البيانات / Update database
python init_db.py

# إعادة تشغيل الخدمة / Restart service
sudo systemctl start billing
```

### 10. استكشاف الأخطاء / Troubleshooting

#### مشاكل شائعة / Common Issues

##### الخدمة لا تعمل / Service Not Starting
```bash
# فحص السجلات / Check logs
sudo journalctl -u billing -n 50

# فحص الإعدادات / Check configuration
sudo systemctl status billing

# إعادة تشغيل / Restart
sudo systemctl restart billing
```

##### مشاكل قاعدة البيانات / Database Issues
```bash
# فحص الاتصال / Check connection
python3 -c "from app import app; from models import db; print('DB OK' if db else 'DB Error')"

# إعادة تهيئة / Reinitialize
python init_db.py --reset
```

##### مشاكل الأذونات / Permission Issues
```bash
# إصلاح الأذونات / Fix permissions
sudo chown -R www-data:www-data /path/to/your/app
sudo chmod -R 755 /path/to/your/app
```

### 11. الأمان المتقدم / Advanced Security

#### جدار الحماية / Firewall
```bash
# تفعيل UFW / Enable UFW
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw status
```

#### حماية من الهجمات / Attack Protection
```bash
# تثبيت Fail2ban / Install Fail2ban
sudo apt install fail2ban -y

# إعداد Fail2ban / Configure Fail2ban
sudo cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[nginx-http-auth]
enabled = true
EOF

sudo systemctl restart fail2ban
```

---

## 📋 قائمة التحقق / Checklist

### قبل النشر / Before Deployment
- [ ] تحديث كلمة مرور المدير / Update admin password
- [ ] إعداد متغيرات البيئة / Set environment variables
- [ ] اختبار النظام محلياً / Test system locally
- [ ] إعداد قاعدة بيانات الإنتاج / Setup production database

### بعد النشر / After Deployment
- [ ] اختبار جميع الوظائف / Test all functions
- [ ] إعداد النسخ الاحتياطي / Setup backups
- [ ] تفعيل المراقبة / Enable monitoring
- [ ] إعداد SSL / Setup SSL
- [ ] اختبار الأداء / Performance testing

### الصيانة الدورية / Regular Maintenance
- [ ] فحص السجلات أسبوعياً / Check logs weekly
- [ ] تحديث النظام شهرياً / Update system monthly
- [ ] فحص النسخ الاحتياطية / Verify backups
- [ ] مراجعة الأمان / Security review

---

## 🎯 الخلاصة / Summary

هذا الدليل يوفر خطوات شاملة لنشر نظام الفواتير في بيئة إنتاج آمنة وموثوقة. تأكد من اتباع جميع خطوات الأمان والمراقبة لضمان تشغيل النظام بكفاءة عالية.

This guide provides comprehensive steps for deploying the billing system in a secure and reliable production environment. Make sure to follow all security and monitoring steps to ensure efficient system operation.
