{% extends "base.html" %}

{% block title %}
    {% if current_language == 'ar' %}المنتجات{% else %}Products{% endif %} - Billing System
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                {% if current_language == 'ar' %}المنتجات{% else %}Products{% endif %}
            </h1>
            <a href="{{ url_for('add_product') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                {% if current_language == 'ar' %}إضافة منتج{% else %}Add Product{% endif %}
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if products.items %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>
                                    {% if current_language == 'ar' %}اسم المنتج{% else %}Product Name{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}الوصف{% else %}Description{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}السعر{% else %}Price{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}معدل الضريبة{% else %}Tax Rate{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}تاريخ الإنشاء{% else %}Created{% endif %}
                                </th>
                                <th>
                                    {% if current_language == 'ar' %}إجراءات{% else %}Actions{% endif %}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products.items %}
                            <tr>
                                <td>
                                    {% if current_language == 'ar' and product.name_ar %}
                                        {{ product.name_ar }}
                                    {% else %}
                                        {{ product.name_en }}
                                    {% endif %}
                                </td>
                                <td>
                                    {% if current_language == 'ar' and product.description_ar %}
                                        {{ product.description_ar[:50] }}{% if product.description_ar|length > 50 %}...{% endif %}
                                    {% else %}
                                        {{ (product.description_en or '')[:50] }}{% if (product.description_en or '')|length > 50 %}...{% endif %}
                                    {% endif %}
                                </td>
                                <td>${{ "%.2f"|format(product.price) }}</td>
                                <td>{{ product.tax_rate }}%</td>
                                <td>{{ product.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="editProduct({{ product.id }})">
                                            <i class="fas fa-edit"></i>
                                            {% if current_language == 'ar' %}تعديل{% else %}Edit{% endif %}
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteProduct({{ product.id }})">
                                            <i class="fas fa-trash"></i>
                                            {% if current_language == 'ar' %}حذف{% else %}Delete{% endif %}
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if products.pages > 1 %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if products.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('products', page=products.prev_num) }}">
                                {% if current_language == 'ar' %}السابق{% else %}Previous{% endif %}
                            </a>
                        </li>
                        {% endif %}

                        {% for page_num in products.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != products.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('products', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if products.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('products', page=products.next_num) }}">
                                {% if current_language == 'ar' %}التالي{% else %}Next{% endif %}
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-box fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">
                        {% if current_language == 'ar' %}لا توجد منتجات{% else %}No products found{% endif %}
                    </h5>
                    <p class="text-muted">
                        {% if current_language == 'ar' %}
                            ابدأ بإضافة منتج جديد
                        {% else %}
                            Start by adding a new product
                        {% endif %}
                    </p>
                    <a href="{{ url_for('add_product') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        {% if current_language == 'ar' %}إضافة منتج{% else %}Add Product{% endif %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function editProduct(productId) {
    // Add edit functionality here
    alert("{{ 'وظيفة التعديل ستتم إضافتها قريباً' if current_language == 'ar' else 'Edit functionality will be added soon' }}");
}

function deleteProduct(productId) {
    const message = "{{ 'هل أنت متأكد من حذف هذا المنتج؟' if current_language == 'ar' else 'Are you sure you want to delete this product?' }}";
    if (confirm(message)) {
        // Add delete functionality here
        alert("{{ 'وظيفة الحذف ستتم إضافتها قريباً' if current_language == 'ar' else 'Delete functionality will be added soon' }}");
    }
}
</script>
{% endblock %}
