{% extends "base.html" %}

{% block title %}
    {% if current_language == 'ar' %}إضافة منتج{% else %}Add Product{% endif %} - Billing System
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                {% if current_language == 'ar' %}إضافة منتج جديد{% else %}Add New Product{% endif %}
            </h1>
            <a href="{{ url_for('products') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                {% if current_language == 'ar' %}العودة{% else %}Back{% endif %}
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    {% if current_language == 'ar' %}معلومات المنتج{% else %}Product Information{% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name_en" class="form-label">
                                {% if current_language == 'ar' %}اسم المنتج (إنجليزي) *{% else %}Product Name (English) *{% endif %}
                            </label>
                            <input type="text" class="form-control" id="name_en" name="name_en" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="name_ar" class="form-label">
                                {% if current_language == 'ar' %}اسم المنتج (عربي){% else %}Product Name (Arabic){% endif %}
                            </label>
                            <input type="text" class="form-control" id="name_ar" name="name_ar" 
                                   {% if current_language == 'ar' %}dir="rtl"{% endif %}>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="price" class="form-label">
                                {% if current_language == 'ar' %}السعر *{% else %}Price *{% endif %}
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control" id="price" name="price" 
                                       step="0.01" min="0" required>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="tax_rate" class="form-label">
                                {% if current_language == 'ar' %}معدل الضريبة (%){% else %}Tax Rate (%){% endif %}
                            </label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="tax_rate" name="tax_rate" 
                                       step="0.01" min="0" max="100" value="0">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="description_en" class="form-label">
                                {% if current_language == 'ar' %}الوصف (إنجليزي){% else %}Description (English){% endif %}
                            </label>
                            <textarea class="form-control" id="description_en" name="description_en" rows="4"></textarea>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="description_ar" class="form-label">
                                {% if current_language == 'ar' %}الوصف (عربي){% else %}Description (Arabic){% endif %}
                            </label>
                            <textarea class="form-control" id="description_ar" name="description_ar" rows="4"
                                      {% if current_language == 'ar' %}dir="rtl"{% endif %}></textarea>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('products') }}" class="btn btn-secondary">
                            {% if current_language == 'ar' %}إلغاء{% else %}Cancel{% endif %}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            {% if current_language == 'ar' %}حفظ{% else %}Save{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    {% if current_language == 'ar' %}نصائح{% else %}Tips{% endif %}
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-info-circle text-primary me-2"></i>
                        {% if current_language == 'ar' %}
                            اسم المنتج والسعر مطلوبان
                        {% else %}
                            Product name and price are required
                        {% endif %}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-percentage text-warning me-2"></i>
                        {% if current_language == 'ar' %}
                            معدل الضريبة اختياري (افتراضي 0%)
                        {% else %}
                            Tax rate is optional (default 0%)
                        {% endif %}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-globe text-info me-2"></i>
                        {% if current_language == 'ar' %}
                            أضف الاسم والوصف بالعربية للفواتير العربية
                        {% else %}
                            Add Arabic name and description for Arabic invoices
                        {% endif %}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-file-invoice text-success me-2"></i>
                        {% if current_language == 'ar' %}
                            يمكن استخدام هذا المنتج في الفواتير
                        {% else %}
                            This product can be used in invoices
                        {% endif %}
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    {% if current_language == 'ar' %}معاينة السعر{% else %}Price Preview{% endif %}
                </h6>
            </div>
            <div class="card-body">
                <div id="price-preview">
                    <p class="mb-1">
                        <strong>
                            {% if current_language == 'ar' %}السعر الأساسي:{% else %}Base Price:{% endif %}
                        </strong> 
                        <span id="base-price">$0.00</span>
                    </p>
                    <p class="mb-1">
                        <strong>
                            {% if current_language == 'ar' %}الضريبة:{% else %}Tax:{% endif %}
                        </strong> 
                        <span id="tax-amount">$0.00</span>
                    </p>
                    <hr>
                    <p class="mb-0">
                        <strong>
                            {% if current_language == 'ar' %}السعر الإجمالي:{% else %}Total Price:{% endif %}
                        </strong> 
                        <span id="total-price">$0.00</span>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updatePricePreview() {
    const price = parseFloat(document.getElementById('price').value) || 0;
    const taxRate = parseFloat(document.getElementById('tax_rate').value) || 0;
    const taxAmount = price * (taxRate / 100);
    const totalPrice = price + taxAmount;
    
    document.getElementById('base-price').textContent = '$' + price.toFixed(2);
    document.getElementById('tax-amount').textContent = '$' + taxAmount.toFixed(2);
    document.getElementById('total-price').textContent = '$' + totalPrice.toFixed(2);
}

document.getElementById('price').addEventListener('input', updatePricePreview);
document.getElementById('tax_rate').addEventListener('input', updatePricePreview);

// Initialize preview
updatePricePreview();
</script>
{% endblock %}
