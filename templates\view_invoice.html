{% extends "base.html" %}

{% block title %}
    {% if current_language == 'ar' %}فاتورة{% else %}Invoice{% endif %} {{ invoice.invoice_number }} - Billing System
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                {% if current_language == 'ar' %}فاتورة{% else %}Invoice{% endif %} {{ invoice.invoice_number }}
            </h1>
            <div class="btn-group no-print" role="group">
                <a href="{{ url_for('invoices') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    {% if current_language == 'ar' %}العودة{% else %}Back{% endif %}
                </a>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-download"></i>
                        {% if current_language == 'ar' %}تحميل PDF{% else %}Download PDF{% endif %}
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="{{ url_for('download_invoice_pdf', id=invoice.id, language='en') }}">
                                <i class="fas fa-file-pdf"></i> English PDF
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{{ url_for('download_invoice_pdf', id=invoice.id, language='ar') }}">
                                <i class="fas fa-file-pdf"></i> Arabic PDF
                            </a>
                        </li>
                    </ul>
                </div>
                <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print"></i>
                    {% if current_language == 'ar' %}طباعة{% else %}Print{% endif %}
                </button>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-edit"></i>
                        {% if current_language == 'ar' %}تغيير الحالة{% else %}Change Status{% endif %}
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <form method="POST" action="{{ url_for('update_invoice_status', id=invoice.id) }}" style="display: inline;">
                                <input type="hidden" name="status" value="draft">
                                <button type="submit" class="dropdown-item">
                                    <i class="fas fa-edit text-warning"></i>
                                    {% if current_language == 'ar' %}مسودة{% else %}Draft{% endif %}
                                </button>
                            </form>
                        </li>
                        <li>
                            <form method="POST" action="{{ url_for('update_invoice_status', id=invoice.id) }}" style="display: inline;">
                                <input type="hidden" name="status" value="sent">
                                <button type="submit" class="dropdown-item">
                                    <i class="fas fa-paper-plane text-info"></i>
                                    {% if current_language == 'ar' %}مرسلة{% else %}Sent{% endif %}
                                </button>
                            </form>
                        </li>
                        <li>
                            <form method="POST" action="{{ url_for('update_invoice_status', id=invoice.id) }}" style="display: inline;">
                                <input type="hidden" name="status" value="paid">
                                <button type="submit" class="dropdown-item">
                                    <i class="fas fa-check text-success"></i>
                                    {% if current_language == 'ar' %}مدفوعة{% else %}Paid{% endif %}
                                </button>
                            </form>
                        </li>
                        <li>
                            <form method="POST" action="{{ url_for('update_invoice_status', id=invoice.id) }}" style="display: inline;">
                                <input type="hidden" name="status" value="cancelled">
                                <button type="submit" class="dropdown-item">
                                    <i class="fas fa-times text-danger"></i>
                                    {% if current_language == 'ar' %}ملغية{% else %}Cancelled{% endif %}
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card invoice-card">
            <div class="card-header invoice-header">
                <div class="row">
                    <div class="col-md-6">
                        <h2 class="text-white mb-0">
                            {% if current_language == 'ar' %}فاتورة{% else %}INVOICE{% endif %}
                        </h2>
                        <p class="text-white-50 mb-0">{{ invoice.invoice_number }}</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <h4 class="text-white">
                            {% if current_language == 'ar' %}
                                شركتك
                            {% else %}
                                Your Company
                            {% endif %}
                        </h4>
                        <p class="text-white-50 mb-0">
                            {% if current_language == 'ar' %}
                                عنوان الشركة<br>
                                الهاتف: +1234567890<br>
                                البريد: <EMAIL>
                            {% else %}
                                Company Address<br>
                                Phone: +1234567890<br>
                                Email: <EMAIL>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="card-body">
                <!-- Invoice Details -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="invoice-details">
                            <h5>
                                {% if current_language == 'ar' %}فوترة إلى:{% else %}Bill To:{% endif %}
                            </h5>
                            <p class="mb-1">
                                <strong>
                                    {% if current_language == 'ar' and invoice.customer.name_ar %}
                                        {{ invoice.customer.name_ar }}
                                    {% else %}
                                        {{ invoice.customer.name_en }}
                                    {% endif %}
                                </strong>
                            </p>
                            {% if invoice.customer.address_en or invoice.customer.address_ar %}
                            <p class="mb-1">
                                {% if current_language == 'ar' and invoice.customer.address_ar %}
                                    {{ invoice.customer.address_ar }}
                                {% else %}
                                    {{ invoice.customer.address_en or '' }}
                                {% endif %}
                            </p>
                            {% endif %}
                            {% if invoice.customer.phone %}
                            <p class="mb-1">
                                {% if current_language == 'ar' %}الهاتف:{% else %}Phone:{% endif %} {{ invoice.customer.phone }}
                            </p>
                            {% endif %}
                            {% if invoice.customer.email %}
                            <p class="mb-1">
                                {% if current_language == 'ar' %}البريد:{% else %}Email:{% endif %} {{ invoice.customer.email }}
                            </p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="invoice-details">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>
                                        {% if current_language == 'ar' %}رقم الفاتورة:{% else %}Invoice Number:{% endif %}
                                    </strong></td>
                                    <td>{{ invoice.invoice_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>
                                        {% if current_language == 'ar' %}تاريخ الإصدار:{% else %}Issue Date:{% endif %}
                                    </strong></td>
                                    <td>{{ invoice.issue_date.strftime('%Y-%m-%d') }}</td>
                                </tr>
                                {% if invoice.due_date %}
                                <tr>
                                    <td><strong>
                                        {% if current_language == 'ar' %}تاريخ الاستحقاق:{% else %}Due Date:{% endif %}
                                    </strong></td>
                                    <td>{{ invoice.due_date.strftime('%Y-%m-%d') }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td><strong>
                                        {% if current_language == 'ar' %}الحالة:{% else %}Status:{% endif %}
                                    </strong></td>
                                    <td>
                                        {% if invoice.status == 'paid' %}
                                            <span class="badge bg-success">
                                                {% if current_language == 'ar' %}مدفوعة{% else %}Paid{% endif %}
                                            </span>
                                        {% elif invoice.status == 'sent' %}
                                            <span class="badge bg-info">
                                                {% if current_language == 'ar' %}مرسلة{% else %}Sent{% endif %}
                                            </span>
                                        {% elif invoice.status == 'cancelled' %}
                                            <span class="badge bg-danger">
                                                {% if current_language == 'ar' %}ملغية{% else %}Cancelled{% endif %}
                                            </span>
                                        {% else %}
                                            <span class="badge bg-warning">
                                                {% if current_language == 'ar' %}مسودة{% else %}Draft{% endif %}
                                            </span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Invoice Items -->
                <div class="invoice-table">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>
                                    {% if current_language == 'ar' %}الوصف{% else %}Description{% endif %}
                                </th>
                                <th class="text-center">
                                    {% if current_language == 'ar' %}الكمية{% else %}Qty{% endif %}
                                </th>
                                <th class="text-end">
                                    {% if current_language == 'ar' %}سعر الوحدة{% else %}Unit Price{% endif %}
                                </th>
                                <th class="text-center">
                                    {% if current_language == 'ar' %}الضريبة{% else %}Tax{% endif %}
                                </th>
                                <th class="text-end">
                                    {% if current_language == 'ar' %}الإجمالي{% else %}Total{% endif %}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in invoice.items %}
                            <tr>
                                <td>
                                    {% if current_language == 'ar' and item.description_ar %}
                                        {{ item.description_ar }}
                                    {% else %}
                                        {{ item.description_en }}
                                    {% endif %}
                                </td>
                                <td class="text-center">{{ item.quantity }}</td>
                                <td class="text-end">${{ "%.2f"|format(item.unit_price) }}</td>
                                <td class="text-center">{{ item.tax_rate }}%</td>
                                <td class="text-end">${{ "%.2f"|format(item.line_total + (item.line_total * item.tax_rate / 100)) }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Invoice Totals -->
                <div class="row">
                    <div class="col-md-6 offset-md-6">
                        <table class="table">
                            <tr>
                                <td><strong>
                                    {% if current_language == 'ar' %}المجموع الفرعي:{% else %}Subtotal:{% endif %}
                                </strong></td>
                                <td class="text-end"><strong>${{ "%.2f"|format(invoice.subtotal) }}</strong></td>
                            </tr>
                            <tr>
                                <td><strong>
                                    {% if current_language == 'ar' %}الضريبة:{% else %}Tax:{% endif %}
                                </strong></td>
                                <td class="text-end"><strong>${{ "%.2f"|format(invoice.tax_amount) }}</strong></td>
                            </tr>
                            <tr class="invoice-total">
                                <td><strong>
                                    {% if current_language == 'ar' %}الإجمالي:{% else %}Total:{% endif %}
                                </strong></td>
                                <td class="text-end"><strong>${{ "%.2f"|format(invoice.total_amount) }}</strong></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- Notes -->
                {% if invoice.notes_en or invoice.notes_ar %}
                <div class="row mt-4">
                    <div class="col-12">
                        <h6>
                            {% if current_language == 'ar' %}ملاحظات:{% else %}Notes:{% endif %}
                        </h6>
                        <p>
                            {% if current_language == 'ar' and invoice.notes_ar %}
                                {{ invoice.notes_ar }}
                            {% else %}
                                {{ invoice.notes_en or '' }}
                            {% endif %}
                        </p>
                    </div>
                </div>
                {% endif %}
                
                <!-- Footer -->
                <div class="row mt-5">
                    <div class="col-12 text-center">
                        <p class="text-muted">
                            {% if current_language == 'ar' %}
                                شكراً لك على عملك معنا!
                            {% else %}
                                Thank you for your business!
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
