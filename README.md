# Python PDF Billing System (Flask) - Bilingual

A comprehensive web-based billing system built with Flask that supports both Arabic and English languages. This system allows businesses to manage customers, products, and generate professional PDF invoices.

## Features

- **User Authentication**: Secure login system with user permissions
- **Dashboard**: Overview of billing statistics and recent activities
- **Customer Management**: Add, edit, and manage customer information in both languages
- **Product Management**: Maintain product catalog with pricing and tax information
- **Invoice Generation**: Create and manage invoices with PDF export capability
- **Bilingual Support**: Full Arabic and English language support
- **PDF Export**: Generate professional invoices in both languages
- **Responsive Design**: Mobile-friendly interface using Bootstrap

## Project Structure

```
├── app.py                 # Main Flask application
├── models.py             # Database models (SQLAlchemy)
├── config.py             # Configuration settings
├── utils.py              # Utility functions for PDF generation
├── requirements.txt      # Python dependencies
├── templates/            # HTML templates
│   ├── base.html         # Base template with navigation
│   ├── login.html        # Login page
│   ├── dashboard.html    # Main dashboard
│   ├── customers.html    # Customer listing
│   ├── add_customer.html # Add customer form
│   ├── edit_customer.html# Edit customer form
│   ├── products.html     # Product listing
│   ├── add_product.html  # Add product form
│   ├── invoices.html     # Invoice listing
│   ├── create_invoice.html# Create invoice form
│   └── view_invoice.html # Invoice display
├── static/               # Static files
│   └── css/
│       └── style.css     # Custom styles
└── README.md            # This documentation
```

## Installation

### Quick Start

1. **Clone or download the project files**

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   python run.py
   ```

   Or alternatively:
   ```bash
   python app.py
   ```

4. **Access the application**:
   Open your browser and go to `http://localhost:2121`

### Manual Database Setup

If you need to reset or manually initialize the database:

```bash
# Initialize database with sample data
python init_db.py

# Reset database (WARNING: This will delete all data)
python init_db.py --reset
```

## Default Login

- **Username**: admin
- **Password**: admin123

## Usage Guide

### 1. Dashboard
- View system statistics (customers, products, invoices, revenue)
- Quick access to recent invoices
- Quick action buttons for common tasks

### 2. Customer Management
- Add new customers with English and Arabic information
- Edit existing customer details
- Search customers by name, email, or phone
- Delete customers (if no invoices exist)
- View customer invoice history

### 3. Product Management
- Add products with pricing and tax information
- Edit existing products
- Support for bilingual product names and descriptions
- Set tax rates per product
- Delete products (if not used in invoices)

### 4. Invoice Creation
- Select customer from dropdown
- Add products from catalog or create custom line items
- Automatic calculation of subtotals, taxes, and totals
- Add notes in both languages
- Real-time price calculations

### 5. Invoice Management
- View all invoices with search and filtering options
- Filter by status (Draft, Sent, Paid, Cancelled)
- Download invoices as PDF in English or Arabic
- Print invoices directly from browser
- Update invoice status
- Track invoice status changes

### 6. Reports
- Generate financial reports with date filtering
- View revenue statistics and breakdowns
- Analyze invoices by status and customer
- Export data to CSV format
- Top customers analysis

### 7. Company Settings
- Configure company information for invoices
- Set bilingual company details
- Upload company logo
- Manage tax settings

## Language Support

The system supports both English and Arabic:

- **Interface Language**: Switch between English and Arabic UI
- **Content Language**: Store customer and product information in both languages
- **PDF Generation**: Generate invoices in either language
- **RTL Support**: Proper right-to-left text direction for Arabic

## PDF Features

- Professional invoice layout
- Company logo support (configurable)
- Bilingual invoice generation
- Automatic Arabic text reshaping for proper display
- Print-friendly formatting

## Database Schema

The system uses SQLite database with the following main tables:

- **Users**: System users and authentication
- **Customers**: Customer information (bilingual)
- **Products**: Product catalog with pricing
- **Invoices**: Invoice headers
- **InvoiceItems**: Invoice line items
- **CompanySettings**: Company information and branding

## Configuration

Edit `config.py` to customize:

- Database connection
- Company information
- Upload folders
- Language settings
- Application settings

## Development Features

- **Flask-Login**: User session management
- **SQLAlchemy**: Database ORM
- **Bootstrap 5**: Responsive UI framework
- **Font Awesome**: Icons
- **ReportLab**: PDF generation
- **Arabic Text Support**: Proper Arabic text handling in PDFs

## Future Enhancements

- Email invoice delivery
- Electronic signatures
- Excel export functionality
- Advanced reporting by date/customer
- Multi-company support
- API endpoints for integration

## Technical Requirements

- Python 3.7+
- Flask 2.3+
- SQLAlchemy
- ReportLab for PDF generation
- Arabic text processing libraries

## Security Notes

- Change default admin password in production
- Use environment variables for sensitive configuration
- Implement proper user roles and permissions
- Use HTTPS in production environment

## Support

This is a complete billing system suitable for:
- Small to medium businesses
- Service providers
- Retail shops
- Any organization needing bilingual invoicing

The system is designed to be easily customizable and extendable based on specific business requirements.
