#!/usr/bin/env python3
"""
Main application runner for the Billing System
This script handles application startup and database initialization
"""

import os
import sys
from app import app
from init_db import init_database

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        'flask', 'flask_sqlalchemy', 'flask_login', 'flask_wtf',
        'reportlab', 'arabic_reshaper', 'bidi'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 Install missing packages with:")
        print("   pip install -r requirements.txt")
        return False
    
    return True

def setup_application():
    """Setup the application and database"""
    print("🚀 Starting Billing System Setup...")
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Check if database exists
    db_path = 'billing_system.db'
    if not os.path.exists(db_path):
        print("📊 Database not found. Creating new database...")
        init_database()
    else:
        print("📊 Database found. Checking tables...")
        try:
            with app.app_context():
                from models import User
                if User.query.count() == 0:
                    print("📊 Database is empty. Adding sample data...")
                    init_database()
                else:
                    print("✅ Database is ready!")
        except Exception as e:
            print(f"⚠️  Database error: {e}")
            print("📊 Reinitializing database...")
            init_database()

def main():
    """Main application entry point"""
    print("=" * 60)
    print("🏢 BILLING SYSTEM - نظام الفواتير")
    print("=" * 60)
    
    # Setup application
    setup_application()
    
    print("\n✅ Application is ready!")
    print("\n📋 System Information:")
    print(f"   🌐 URL: http://localhost:2121")
    print(f"   👤 Default Username: admin")
    print(f"   🔑 Default Password: admin123")
    print(f"   🗂️  Database: billing_system.db")
    
    print("\n🌟 Features:")
    print("   ✓ Bilingual support (English/Arabic)")
    print("   ✓ Customer management")
    print("   ✓ Product catalog")
    print("   ✓ Invoice generation")
    print("   ✓ PDF export (Arabic/English)")
    print("   ✓ Financial reports")
    print("   ✓ Company settings")
    
    print("\n🚀 Starting Flask development server...")
    print("   Press Ctrl+C to stop the server")
    print("=" * 60)
    
    try:
        # Run the Flask application
        app.run(
            host='0.0.0.0',
            port=2121,
            debug=True,
            use_reloader=False  # Disable reloader to prevent double initialization
        )
    except KeyboardInterrupt:
        print("\n\n👋 Shutting down the server...")
        print("Thank you for using the Billing System!")
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
